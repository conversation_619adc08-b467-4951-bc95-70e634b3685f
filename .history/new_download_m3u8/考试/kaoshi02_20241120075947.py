import difflib
from docx import Document
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import json
import hashlib
import logging
from fuzzywuzzy import fuzz
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import tkinter.font as tkfont
import time

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kaoshi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 读取文本文件
def read_txt_file(file_path):
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return [line.strip() for line in file.readlines() if line.strip()]
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题���和题库
def load_questions(file_path):
    if not (file_path.endswith('.txt') or file_path.endswith('.docx')):
        raise ValueError('错: 不支持的文件格式，请使用 .txt 或 .docx 文件。')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 从JSON文件加载题库
def load_question_bank():
    try:
        with open('question_bank.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return None
    except json.JSONDecodeError:
        return None

# 生成比较后的文档文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建四列表格
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 设置表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '考试题目'
        hdr_cells[1].text = '题目类型'
        hdr_cells[2].text = '匹配答案'
        hdr_cells[3].text = '匹配状态'
        
        # 添加数据
        for question, answer, score in matched_answers:
            if not question.strip():
                continue
            
            row_cells = table.add_row().cells
            row_cells[0].text = question.strip()
            row_cells[1].text = ""
            row_cells[2].text = answer.strip()
            row_cells[3].text = "匹配成功" if score == 100 else "未匹配"
        
        doc.save(output_path)
        return True
    except Exception as e:
        logging.error(f"保存文件时发生错误: {e}")
        return False

# 处理参考题目，创建或更新题库
def process_reference_questions(reference_file):
    try:
        if not os.path.exists(reference_file):
            raise FileNotFoundError("选择的文件不存在")
            
        questions = load_questions(reference_file)
        if not questions:
            raise ValueError("未能从文件中读取到任何题目")
            
        question_bank = {}
        
        for question in questions:
            if not question.strip():
                continue
                
            # 使用MD5作为题目的唯一标识
            question_hash = hashlib.md5(question.encode('utf-8')).hexdigest()
            question_bank[question_hash] = {
                'question': question,
                'type': '未分类',  # 默认题目类型
                'answer': question  # 在这里，我们将题目本身作为答案存储
            }
        
        if not question_bank:
            raise ValueError("处理后的题库为空")
            
        # 保存题库到JSON文件
        with open('question_bank.json', 'w', encoding='utf-8') as f:
            json.dump(question_bank, f, ensure_ascii=False, indent=4)
            
        logging.info(f"题库已更新，共包含 {len(question_bank)} 个题目")
        return True, f"题库已更新，共包含 {len(question_bank)} 个题目"
        
    except Exception as e:
        error_msg = f"处理参考题目时发生错误: {str(e)}"
        logging.error(error_msg)
        return False, error_msg

# 匹配题目与答案
def match_question_to_answer(questions, question_bank):
    try:
        if not questions:
            raise ValueError("没有要匹配的题目")
        if not question_bank:
            raise ValueError("题库为空")
            
        matched_answers = []
        
        for question in questions:
            if not question.strip():
                continue
                
            best_match = None
            best_score = 0
            
            for q_hash, q_data in question_bank.items():
                try:
                    # 使用模糊匹配计算相似度
                    score = fuzz.ratio(question.lower(), q_data['question'].lower())
                    
                    if score > best_score:
                        best_score = score
                        best_match = q_data
                except Exception as e:
                    logging.warning(f"计算题目相似度时发生错误: {e}")
                    continue
            
            if best_match and best_score >= 80:  # 设置匹配阈值为80%
                matched_answers.append((question, best_match['answer'], best_score))
            else:
                matched_answers.append((question, "未找到匹配答案", 0))
            
            logging.debug(f"题目: {question[:30]}... 最佳匹配分数: {best_score}")
        
        return matched_answers
        
    except Exception as e:
        logging.error(f"匹配题目时发生错误: {e}")
        raise

# 主程序 (GUI 界面)
def main():
    try:
        def update_message(message, level="info"):
            # 在消息框中添加新消息，支持不同级别的消息显示
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            message_text.configure(state='normal')
            
            # 根据消息级别设置不同的标签
            if level == "error":
                tag = "error"
                prefix = "错误"
            elif level == "warning":
                tag = "warning"
                prefix = "警告"
            elif level == "success":
                tag = "success"
                prefix = "成功"
            else:
                tag = "info"
                prefix = "信息"
                
            message_text.insert(tk.END, f"[{timestamp}] [{prefix}] {message}\n", tag)
            message_text.see(tk.END)  # 自动滚动到最新消息
            message_text.configure(state='disabled')
            root.update()

        def create_or_update_question_bank():
            try:
                reference_file = filedialog.askopenfilename(title='选择题库文件', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
                if reference_file:
                    update_message(f"选择题库文件: {reference_file}")
                    success, message = process_reference_questions(reference_file)
                    if success:
                        update_message(message, "success")
                    else:
                        update_message(message, "error")
            except Exception as e:
                update_message(f"创建/更新题库时发生错误: {str(e)}", "error")
                logging.error(f"创建/更新题库时发生错误: {e}")

        def view_question_bank():
            try:
                question_bank = load_question_bank()
                if not question_bank:
                    update_message("题库文件不存在或格式错误，请先创建题库", "error")
                    return
                    
                update_message("正在加载题库内容...")
                content = json.dumps(question_bank, ensure_ascii=False, indent=4)
                
                # 创建新窗口显示题库内容
                view_window = tk.Toplevel(root)
                view_window.title("题库内容")
                view_window.geometry("600x400")
                
                text_widget = tk.Text(view_window, wrap=tk.WORD, font=('Arial', 10))
                scrollbar = ttk.Scrollbar(view_window, orient="vertical", command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)
                
                text_widget.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")
                
                text_widget.insert("1.0", content)
                text_widget.configure(state='disabled')
                update_message("题库内容已加载完成", "success")
            except Exception as e:
                update_message(f"查看题库时发生错误: {str(e)}", "error")
                logging.error(f"查看题库时发生错误: {e}")

        def match_questions():
            try:
                question_bank = load_question_bank()
                if not question_bank:
                    update_message("题库为空或无法加载，请先创建题库", "error")
                    return
                
                questions_file = filedialog.askopenfilename(title='选择考试题目文件', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
                if questions_file:
                    update_message(f"选择考试题目文件: {questions_file}")
                    questions = load_questions(questions_file)
                    if not questions:
                        update_message("未能读取到任何题目", "warning")
                        return
                    
                    update_message("正在匹配题目...")
                    matched_answers = match_question_to_answer(questions, question_bank)
                    update_message(f"匹配完成，共处理 {len(matched_answers)} 个题目", "success")
                    
                    output_file = filedialog.asksaveasfilename(title='保存结果文件', defaultextension=".docx", filetypes=[("Word files", "*.docx")])
                    if output_file:
                        update_message(f"正在保存结果到: {output_file}")
                        if generate_result_file(matched_answers, output_file):
                            update_message("结果文件保存成功！", "success")
                        else:
                            update_message("结果文件保存失败！", "error")
            except Exception as e:
                update_message(f"匹配题目时发生错误: {str(e)}", "error")
                logging.error(f"匹配题目时发生错误: {e}")

        def close_app():
            root.destroy()

        # 创建主窗口
        root = tk.Tk()
        root.title("题目与答案匹配工具")
        root.geometry("800x500")  # 加宽窗口��适应左右布局
        
        # 设置窗口样式
        style = ttk.Style()
        style.configure('TButton', font=('Arial', 10))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        
        # 创建左右分隔面板
        paned_window = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 控制区域
        left_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(left_frame, weight=1)
        
        # 右侧面板 - 消息区域
        right_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(right_frame, weight=1)
        
        # 左侧内容
        # 添加标题
        header = ttk.Label(left_frame, text="题目与答案匹配工具", style='Header.TLabel')
        header.pack(pady=(0, 20))
        
        # 创建按钮框架
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.BOTH, expand=True)
        
        # 定义按钮样式
        button_style = {'width': 20, 'padding': 10}
        
        # 添加按钮
        ttk.Button(button_frame, text="创建/更新题库", command=create_or_update_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="查看题库", command=view_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="匹配题目", command=match_questions, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="关闭程序", command=close_app, **button_style).pack(pady=10)
        
        # 右侧消息框
        message_frame = ttk.LabelFrame(right_frame, text="消息记录", padding="5")
        message_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建消息文本框和滚动条
        message_text = tk.Text(message_frame, wrap=tk.WORD, font=('Arial', 10))
        message_scrollbar = ttk.Scrollbar(message_frame, orient="vertical", command=message_text.yview)
        message_text.configure(yscrollcommand=message_scrollbar.set)
        
        message_text.pack(side="left", fill="both", expand=True)
        message_scrollbar.pack(side="right", fill="y")
        
        # 设置消息框为只读
        message_text.configure(state='disabled')
        
        # 添加初始消息
        update_message("程序已启动，等待操作...")
        
        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # 配置消息文本框的标签样式
        message_text.tag_configure("error", foreground="red")
        message_text.tag_configure("warning", foreground="orange")
        message_text.tag_configure("success", foreground="green")
        message_text.tag_configure("info", foreground="black")
        
        root.mainloop()

    except Exception as e:
        logging.error(f"程序运行时发生错误: {e}")
        messagebox.showerror("错误", f"程序运行时发生错误: {str(e)}")

if __name__ == "__main__":
    main()
