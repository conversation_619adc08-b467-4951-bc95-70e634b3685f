import difflib
from docx import Document
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

# 读取文本文件
def read_txt_file(file_path):
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return [line.strip() for line in file.readlines() if line.strip()]
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题目和题库
def load_questions(file_path):
    if not (file_path.endswith('.txt') or file_path.endswith('.docx')):
        raise ValueError('错误: 不支持的文件格式，请使用 .txt 或 .docx 文件。')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 匹配题目与参考答案
def match_question_to_answer(questions, reference_questions):
    matched_answers = []
    for i, question in enumerate(questions):
        if i < len(reference_questions):
            matched_answers.append((question, reference_questions[i], 100))
        else:
            matched_answers.append((question, "未找到匹配的参考答案", 0))
    return matched_answers

# 生成比较后的答案文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        # 添加标题
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建表格
        table = doc.add_table(rows=1, cols=3)
        table.style = 'Table Grid'
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 设置表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '题目'
        hdr_cells[1].text = '匹配的参考答案'
        hdr_cells[2].text = '匹配度'
        
        # 添加数据
        for idx, (question, answer, score) in enumerate(matched_answers, start=1):
            if question.strip() and answer.strip():
                row_cells = table.add_row().cells
                row_cells[0].text = f"{idx}. {question.strip()}"
                formatted_answer = (
                    f"题目：{question.strip()}\n"
                    f"答案：{answer.strip()}"
                )
                row_cells[1].text = formatted_answer
                row_cells[2].text = f"{score}%"
                
                # 设置单元格内文本格式
                for cell in row_cells:
                    paragraphs = cell.paragraphs
                    for paragraph in paragraphs:
                        paragraph.paragraph_format.space_before = Pt(6)
                        paragraph.paragraph_format.space_after = Pt(6)
                        for run in paragraph.runs:
                            run.font.size = Pt(10)
        
        doc.save(output_path)
    except Exception as e:
        print(f"保存文件时发生错误: {e}")

# 主函数
def main():
    try:
        print("\n=== 题目与答案匹配工具 ===\n")
        
        # 输入文件路径
        while True:
            questions_file = input('请输入题目文件路径 (.txt 或 .docx): ').strip()
            if os.path.isfile(questions_file):
                break
            print(f"错误: 文件不存在，请重新输入")

        while True:
            reference_file = input('请输入题库文件路径 (.txt 或 .docx): ').strip()
            if os.path.isfile(reference_file):
                break
            print(f"错误: 文件不存在，请重新输入")

        output_file = input('请输入输出文件路径 (.docx): ').strip()
        if not output_file.endswith('.docx'):
            output_file += '.docx'

        # 加载文件
        print("\n正在加载文件...")
        questions = load_questions(questions_file)
        reference_questions = load_questions(reference_file)
        print(f"已加载题目 {len(questions)} 个，参考答案 {len(reference_questions)} 个")

        # 比对答案
        print("正在比对题目和答案...")
        matched_answers = match_question_to_answer(questions, reference_questions)

        # 生成结果
        print("正在生成结果文件...")
        generate_result_file(matched_answers, output_file)
        print(f'\n完成！结果已保存到: {output_file}')
        
    except KeyboardInterrupt:
        print("\n程序已取消")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
