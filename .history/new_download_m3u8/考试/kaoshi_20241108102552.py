import difflib
from docx import Document
from fuzzywuzzy import fuzz
import os
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
import logging
import argparse
import json

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kaoshi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 配置
CONFIG = {
    'MIN_MATCH_SCORE': 50,  # 最低匹配分数
    'FONT_SIZE': 10,        # 字体大小
    'TABLE_STYLE': 'Table Grid',  # 表格样式
    'SUPPORTED_FORMATS': ('.txt', '.docx'),  # 支持的文件格式
    'QUESTIONS_FILE': './questions.txt',     # 题目文件路径
    'REFERENCE_FILE': './reference.docx',    # 题库文件路径
    'OUTPUT_FILE': './result.docx'           # 输出文件路径
}

# 读取文本文件
def read_txt_file(file_path):
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return [line.strip() for line in file.readlines() if line.strip()]
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法使用常用编码（{", ".join(encodings)}）读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题目和题库
def load_questions(file_path):
    if not any(file_path.endswith(fmt) for fmt in CONFIG['SUPPORTED_FORMATS']):
        raise ValueError(f'错误: 不支持的文件格式，请使用以下格式: {", ".join(CONFIG["SUPPORTED_FORMATS"])}')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 匹配题目与参考答案
def match_question_to_answer(questions, reference_questions):
    matched_answers = []
    for question in questions:
        best_match = None
        highest_score = 0
        
        for ref_question in reference_questions:
            # 使用 fuzzywuzzy 计算文本相似度
            score = fuzz.ratio(question.strip(), ref_question.strip())
            if score > highest_score:
                highest_score = score
                best_match = ref_question
        
        if best_match and highest_score > CONFIG['MIN_MATCH_SCORE']:  # 使用配置
            matched_answers.append((question, best_match, highest_score))
        else:
            matched_answers.append((question, "未找到匹配的参考答案", 0))
            
    return matched_answers

# 生成比较后的答案文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        # 添加标题
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建表格
        table = doc.add_table(rows=1, cols=3)
        table.style = CONFIG['TABLE_STYLE']
        # 表格居中对齐
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '题目'
        hdr_cells[1].text = '匹配的参考答案'
        hdr_cells[2].text = '匹配相似度'
        
        for idx, (question, answer, score) in enumerate(matched_answers, start=1):
            if question.strip() and answer.strip():  # 排除空白项
                row_cells = table.add_row().cells
                row_cells[0].text = question
                row_cells[1].text = f"{idx}. {answer.strip()}"
                row_cells[2].text = f"{score}%"
                
                # ��置单元格字体大小
                for cell in row_cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.size = Pt(CONFIG['FONT_SIZE'])
        
        # 添加一些视觉分隔符，使得表格更易读
        for row in table.rows:
            for cell in row.cells:
                cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
        
        # 设置表格样式
        for row in table.rows:
            for cell in row.cells:
                # 设置单元格边框
                for paragraph in cell.paragraphs:
                    paragraph.paragraph_format.space_before = Pt(6)
                    paragraph.paragraph_format.space_after = Pt(6)
                # 设置背景色（如果是表头）
                if row == table.rows[0]:
                    cell._tc.get_or_add_tcPr().append(parse_xml(r'<w:shd {} w:fill="EEEEEE"/>'.format(nsdecls('w'))))
        
        doc.save(output_path)
    except PermissionError:
        print(f"错误: 无法保存文件到 '{output_path}'，请检查是否有写入权限或文件是否正在被其他程序使用。")
    except Exception as e:
        print(f"错误: 保存文件时发生意外错误: {e}")

# 修改 main 函数，使用交互式输入
def main():
    try:
        print("\n=== 题目与答案匹配工具 ===\n")
        
        # 交互式输入文件路径
        while True:
            questions_file = input('请输入题目文件路径 (.txt 或 .docx): ').strip()
            if os.path.isfile(questions_file):
                break
            print(f"错误: 文件 '{questions_file}' 不存在，请重新输入")

        while True:
            reference_file = input('请输入题库文件路径 (.txt 或 .docx): ').strip()
            if os.path.isfile(reference_file):
                break
            print(f"错误: 文件 '{reference_file}' 不存在，请重新输入")

        while True:
            output_file = input('请输入输出文件路径 (.docx): ').strip()
            if not output_file.endswith('.docx'):
                output_file += '.docx'
            if not os.path.isdir(os.path.dirname(output_file or '.')):
                print(f"错误: 输出目录不存在，请重新输入")
                continue
            break

        print("\n正在加载文件...")
        try:
            questions = load_questions(questions_file)
            reference_questions = load_questions(reference_file)
            print(f"成功加载题目 {len(questions)} 个，参考答案 {len(reference_questions)} 个")
        except ValueError as e:
            print(e)
            return

        print("正在比对题目和答案...")
        matched_answers = match_question_to_answer(questions, reference_questions)

        print("正在生成结果文件...")
        generate_result_file(matched_answers, output_file)
        print(f'\n比对完成！结果已保存到 {output_file}')
        
    except KeyboardInterrupt:
        print("\n程序已取消")
    except Exception as e:
        print(f"\n程序运行出错: {str(e)}")

if __name__ == "__main__":
    main()
