import difflib
from docx import Document
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import json
import hashlib
import logging
from fuzzywuzzy import fuzz
import tkinter as tk
from tkinter import filedialog, messagebox

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kaoshi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 读取文本文件
def read_txt_file(file_path):
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return [line.strip() for line in file.readlines() if line.strip()]
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    doc = Document(file_path)
    return [para.text for para in doc.paragraphs if para.text.strip()]

# 加载题目和题库
def load_questions(file_path):
    if not (file_path.endswith('.txt') or file_path.endswith('.docx')):
        raise ValueError('错: 不支持的文件格式，请使用 .txt 或 .docx 文件。')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 从JSON文件加载题库
def load_question_bank():
    try:
        with open('question_bank.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        messagebox.showerror("错误", "题库文件不存在，请先创建题库")
        return None
    except json.JSONDecodeError:
        messagebox.showerror("错误", "题库文件格式错误")
        return None

# 生成比较后的文档文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建四列表格
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 设置表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '考试题目'
        hdr_cells[1].text = '题目类型'
        hdr_cells[2].text = '匹配答案'
        hdr_cells[3].text = '匹配状态'
        
        # 添加数据
        for question, answer, score in matched_answers:
            if not question.strip():
                continue
            
            row_cells = table.add_row().cells
            row_cells[0].text = question.strip()
            row_cells[1].text = ""
            row_cells[2].text = answer.strip()
            row_cells[3].text = "匹配成功" if score == 100 else "未匹配"
        
        doc.save(output_path)
        messagebox.showinfo("成功", f"结果已保存到: {output_path}")
    except Exception as e:
        messagebox.showerror("错误", f"保存文件时发生错误: {e}")

# 处理参考题目，创建或更新题库
def process_reference_questions(reference_file):
    try:
        questions = load_questions(reference_file)
        question_bank = {}
        
        for question in questions:
            # 使用MD5作为题目的唯一标识
            question_hash = hashlib.md5(question.encode('utf-8')).hexdigest()
            question_bank[question_hash] = {
                'question': question,
                'type': '',  # 可以根据需要添加题目类型
                'answer': question  # 在这里，我们将题目本身作为答案存储
            }
        
        # 保存题库到JSON文件
        with open('question_bank.json', 'w', encoding='utf-8') as f:
            json.dump(question_bank, f, ensure_ascii=False, indent=4)
            
        logging.info(f"题库已更新，共包含 {len(question_bank)} 个题目")
        return True
    except Exception as e:
        logging.error(f"处理参考题目时发生错误: {e}")
        messagebox.showerror("错误", f"处理参考题目时发生错误: {e}")
        return False

# 匹配题目与答案
def match_question_to_answer(questions, question_bank):
    matched_answers = []
    
    for question in questions:
        best_match = None
        best_score = 0
        
        for q_hash, q_data in question_bank.items():
            # 使用模糊匹配计算相似度
            score = fuzz.ratio(question.lower(), q_data['question'].lower())
            
            if score > best_score:
                best_score = score
                best_match = q_data
        
        if best_match and best_score >= 80:  # 设置匹配阈值为80%
            matched_answers.append((question, best_match['answer'], best_score))
        else:
            matched_answers.append((question, "未找到匹配答案", 0))
        
        logging.debug(f"题目: {question[:30]}... 最佳匹配分数: {best_score}")
    
    return matched_answers

# 主程序 (GUI 界面)
def main():
    def create_or_update_question_bank():
        reference_file = filedialog.askopenfilename(title='选择题库文件', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
        if reference_file:
            process_reference_questions(reference_file)
            messagebox.showinfo("成功", "题库已更新")
    
    def view_question_bank():
        question_bank = load_question_bank()
        if question_bank:
            messagebox.showinfo("题库内容", json.dumps(question_bank, ensure_ascii=False, indent=4))
    
    def match_questions():
        question_bank = load_question_bank()
        if not question_bank:
            return
        
        questions_file = filedialog.askopenfilename(title='选择考试题目文件', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
        if questions_file:
            questions = load_questions(questions_file)
            matched_answers = match_question_to_answer(questions, question_bank)
            output_file = filedialog.asksaveasfilename(title='保存结果文件', defaultextension=".docx", filetypes=[("Word files", "*.docx")])
            if output_file:
                generate_result_file(matched_answers, output_file)
    
    def close_app():
        root.destroy()
    
    root = tk.Tk()
    root.title("题目与答案匹配工具")
    root.geometry("400x300")
    
    tk.Button(root, text="创建/更新题库", command=create_or_update_question_bank).pack(pady=10)
    tk.Button(root, text="查看题库", command=view_question_bank).pack(pady=10)
    tk.Button(root, text="匹配题目", command=match_questions).pack(pady=10)
    tk.Button(root, text="关闭程序", command=close_app).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()
