import difflib
from docx import Document
import os
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import json
import hashlib
import logging
from fuzzywuzzy import fuzz
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import tkinter.font as tkfont
import time
import re

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kaoshi.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 在文件开头添加这个类
class MessageManager:
    def __init__(self, text_widget, root):
        self.text_widget = text_widget
        self.root = root
        
        # 配置文本标签样式
        self.text_widget.tag_configure("error", foreground="red")
        self.text_widget.tag_configure("warning", foreground="orange")
        self.text_widget.tag_configure("success", foreground="green")
        self.text_widget.tag_configure("info", foreground="black")
    
    def add_message(self, message, level="info"):
        try:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            
            if level == "error":
                prefix = "错误"
            elif level == "warning":
                prefix = "警告"
            elif level == "success":
                prefix = "成功"
            else:
                prefix = "信息"
            
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, f"[{timestamp}] [{prefix}] {message}\n", level)
            self.text_widget.see(tk.END)
            self.text_widget.configure(state='disabled')
            self.root.update()
        except Exception as e:
            print(f"添加消息时出错: {e}")
    
    def clear_messages(self):
        """清除所有消息"""
        try:
            self.text_widget.configure(state='normal')
            self.text_widget.delete(1.0, tk.END)
            self.text_widget.configure(state='disabled')
            self.root.update()
        except Exception as e:
            print(f"清除消息时出错: {e}")

# 读取文本文件
def read_txt_file(file_path):
    """读取文本文件，将完整题目组合在一起"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                content = file.read()
                # 使用正则表达式分割题目，处理题号和换行的情况
                questions = re.split(r'\n(?=\d+、\n?)', content)
                # 清理每个题目，合并多行
                cleaned_questions = []
                for q in questions:
                    if q.strip():
                        # 移除多余的换行，保持题目格式
                        q = re.sub(r'\n+', '\n', q.strip())
                        # 确保题号和题目在同一行
                        q = re.sub(r'^(\d+、)\n', r'\1', q)
                        cleaned_questions.append(q)
                return cleaned_questions
        except UnicodeDecodeError:
            continue
    raise ValueError(f'无法读取文件，请检查文件编码。')

# 读取Word文件
def read_docx_file(file_path):
    """读取Word文件，将完整题目组合在一起"""
    doc = Document(file_path)
    content = '\n'.join(para.text for para in doc.paragraphs if para.text.strip())
    # 使用正则表达式分割题目
    questions = re.split(r'\n(?=\d+、)', content)
    # 清理每个题目
    questions = [q.strip() for q in questions if q.strip()]
    return questions

# 加载题和题库
def load_questions(file_path):
    if not (file_path.endswith('.txt') or file_path.endswith('.docx')):
        raise ValueError('错: 不支持的文件格式��请使用 .txt 或 .docx 文件。')
    
    if file_path.endswith('.txt'):
        return read_txt_file(file_path)
    elif file_path.endswith('.docx'):
        return read_docx_file(file_path)

# 从JSON文件加载题库
def load_question_bank():
    try:
        with open('question_bank.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return None
    except json.JSONDecodeError:
        return None

# 生成比较后的文档文件
def generate_result_file(matched_answers, output_path):
    if not output_path.endswith('.docx'):
        output_path += '.docx'
    
    try:
        doc = Document()
        title = doc.add_heading('题目与参考答案匹配结果', level=1)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 创建四列表格
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 设置表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '考试题目'
        hdr_cells[1].text = '匹配分数'
        hdr_cells[2].text = '匹配案'
        hdr_cells[3].text = '匹配'
        
        # 添加数据
        for question, answer, score in matched_answers:
            if not question.strip():
                continue
            
            row_cells = table.add_row().cells
            row_cells[0].text = question.strip()
            row_cells[1].text = f"{score}%" if score > 0 else "0%"
            row_cells[2].text = answer.strip()
            
            # 根据匹配分数确定状态
            if score >= 80:
                status = "完全匹配"
            elif score >= 60:
                status = "部分匹配"
            else:
                status = "未匹配"
            row_cells[3].text = status
        
        doc.save(output_path)
        return True
    except Exception as e:
        logging.error(f"保存文件时发生错误: {e}")
        return False

# 提取题目标题部分，不包含选项和答案
def extract_title(text):
    """提取题目标题部分，不包含选项和答案"""
    # 移除多余的空白字符和换行
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 匹配题目格式：数字、题目内容
    question_match = re.match(r'^\d+、\s*(.+?)(?=\s*(?:单选题|多选题|判断题|\(\d+分\)|A\.|$))', text)
    if question_match:
        # 只返回题目内容分，并移除末尾可能的"单"、"多"等字符
        content = question_match.group(1).strip()
        content = re.sub(r'\s*[单多判]\s*$', '', content)  # 移除末尾的"单"、"多"、"判"
        logging.debug(f"提取到题目内容: {content}")
        return content
    
    # 如果没有找到标准格式，尝试提取第一个选项前的内容
    alt_match = re.split(r'\s*(?:单选题|多选题|判断题|\(\d+分\)|A\.)', text)[0]
    alt_match = re.sub(r'^\d+、\s*', '', alt_match)  # 移除题号
    alt_match = re.sub(r'\s*[单多判]\s*$', '', alt_match)  # 移除末尾的"单"、"多"、"判"
    
    if alt_match:
        logging.debug(f"使用替代方式提取题目内容: {alt_match.strip()}")
        return alt_match.strip()
    
    # 如果都失败了，返回原始文本
    logging.warning(f"无法提取题目内容，使用原始文本: {text}")
    return text

# 清理文本，只保留题目内容相关的文字
def clean_text(text):
    """清理文本，只保留题目容相关的文字"""
    # 移除空白字符
    cleaned = text.strip()
    # 移除题号
    cleaned = re.sub(r'^\d+、\s*', '', cleaned)
    # 移除题型和分数标记
    cleaned = re.sub(r'单选题|多选题|判断题', '', cleaned)
    cleaned = re.sub(r'\(\d+分\)', '', cleaned)
    # 移除选项部分（从A.开始的所有内容）
    cleaned = re.split(r'\s*A\.', cleaned)[0]
    # 移除特殊字符，但保留中文、英文和基本标点
    cleaned = re.sub(r'[^\w\s\u4e00-\u9fff，？]', '', cleaned)
    # 替空格为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    return cleaned.strip().lower()

# 处理参考题目，创建或更新题库
def process_reference_questions(reference_file):
    """处理参考题目，创建或更新题库"""
    try:
        if not os.path.exists(reference_file):
            raise FileNotFoundError("选择的文件不存在")
            
        questions = load_questions(reference_file)
        if not questions:
            raise ValueError("未能从文件中读取到任何题目")
            
        question_bank = {}
        
        for question in questions:
            if not question.strip():
                continue
            
            # 如果是答案格式（A. B. C. D.），跳
            if re.match(r'^[A-D]\.', question.strip()):
                continue
                
            # 提取题目内容
            title = extract_title(question)
            if not title:  # 如果提取失败，跳过
                continue
                
            # 获取答案（假设答案在下一行）
            answer = ""
            try:
                idx = questions.index(question)
                if idx + 1 < len(questions):
                    next_line = questions[idx + 1]
                    if re.match(r'^[A-D]\.', next_line.strip()):
                        answer = next_line.strip()
            except:
                pass
                
            # 使用题目内容作为键
            question_hash = hashlib.md5(title.encode('utf-8')).hexdigest()
            question_bank[question_hash] = {
                'question': question,
                'title': title,
                'answer': answer if answer else question
            }
            
            # 打印调试信息
            print(f"\n处理题目:")
            print(f"原始题目: {question}")
            print(f"提取标题: {title}")
            print(f"匹配答案: {answer}")
            
        if not question_bank:
            raise ValueError("处理后题库为空")
            
        # 保存题库到JSON文件
        with open('question_bank.json', 'w', encoding='utf-8') as f:
            json.dump(question_bank, f, ensure_ascii=False, indent=4)
            
        logging.info(f"题库已更新，共包含 {len(question_bank)} 个题目")
        return True, f"题库已更新，共包含 {len(question_bank)} 个题目"
        
    except Exception as e:
        error_msg = f"处理参考题目时发生错误: {str(e)}"
        logging.error(error_msg)
        return False, error_msg

# 匹配题目与答案
def match_question_to_answer(questions, question_bank):
    """匹配题目与答案"""
    try:
        if not questions:
            raise ValueError("没有要匹配的题目")
        if not question_bank:
            raise ValueError("题库为空")
            
        matched_answers = []
        total_questions = len(questions)
        
        print(f"\n开始匹配，共有 {total_questions} 个题目需要匹配")
        
        for index, question in enumerate(questions, 1):
            if not question.strip():
                continue
                
            # 如果是答案格式，跳过
            if re.match(r'^[A-D]\.', question.strip()):
                continue
                
            best_match = None
            best_score = 0
            
            # 提取题目内容
            current_title = extract_title(question)
            if not current_title:
                continue
                
            print(f"\n处理第 {index}/{total_questions} 个题目:")
            print(f"原始目: {question}")
            print(f"提取的标题: {current_title}")
            
            # 尝试匹配
            for q_hash, q_data in question_bank.items():
                try:
                    bank_title = q_data['title']
                    
                    # 使用多种匹配方法
                    ratio_score = fuzz.ratio(current_title.lower(), bank_title.lower())
                    partial_score = fuzz.partial_ratio(current_title.lower(), bank_title.lower())
                    token_sort_score = fuzz.token_sort_ratio(current_title.lower(), bank_title.lower())
                    
                    # 取最高分数
                    score = max(ratio_score, partial_score, token_sort_score)
                    
                    if score > 80:  # 只显示高分匹配
                        print(f"潜在匹配:")
                        print(f"分数: {score}%")
                        print(f"题库题目: {bank_title}")
                        print(f"题答案: {q_data['answer']}")
                    
                    if score > best_score:
                        best_score = score
                        best_match = q_data
                        
                except Exception as e:
                    print(f"计算相似度时出错: {e}")
                    continue
            
            # 使用较高的匹配阈值
            if best_match and best_score >= 80:  # 提高匹配阈值
                matched_answers.append((
                    question,
                    best_match['answer'],
                    best_score
                ))
                print(f"\n匹配成功! 分数: {best_score}%")
                print(f"匹配答案: {best_match['answer']}")
            else:
                matched_answers.append((question, "未找到匹配答案", 0))
                print(f"\n未找到合适的匹配 (高分数: {best_score}%)")
            
        return matched_answers
        
    except Exception as e:
        error_msg = f"匹配题目时发生错误: {e}"
        print(error_msg)
        logging.error(error_msg)
        raise

def test_single_match(msg_manager, num_questions=6):
    """测试指定数量的题目匹配"""
    try:
        # 让用户选择题目文件
        questions_file = filedialog.askopenfilename(
            title='选择测试题目文件',
            filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")]
        )
        
        if not questions_file:
            return "未选择题目文件"
            
        # 加载题目
        all_test_questions = load_questions(questions_file)
        if not all_test_questions:
            return "未能从文件中读取到题目"
            
        # 加载题库
        test_bank = load_question_bank()
        if not test_bank:
            return "未能加载题库，请先创建或更新题库"
            
        # 确保不超过可用的题目数量
        num_questions = min(num_questions, len(all_test_questions))
        test_questions = all_test_questions[:num_questions]
        
        results = []
        msg_manager.add_message(f"\n=== 开始测试 {num_questions} 道题目的匹配 ===", "info")

        # 加载题库后，先显示题库内容
        msg_manager.add_message("\n=== 题库内容 ===", "info")
        for q_hash, bank_item in test_bank.items():
            if isinstance(bank_item, dict):
                msg_manager.add_message(f"\n题库题目: {bank_item.get('question', '')}", "info")
                msg_manager.add_message(f"题库答案: {bank_item.get('answer', '')}", "info")
                msg_manager.add_message("-" * 50, "info")

        for i, test_question in enumerate(test_questions, 1):
            msg_manager.add_message(f"\n正在处理第 {i}/{num_questions} 题:", "info")
            msg_manager.add_message(f"当前题目:\n{test_question}", "info")

            # 提取题目内容
            question_content = extract_title(test_question)
            msg_manager.add_message(f"提取的题目内容: {question_content}", "info")

            # 最佳匹配记录
            best_match = None
            best_score = 0

            # 遍历题库中的每个题目进行匹配
            msg_manager.add_message("\n开始与题库进行匹配:", "info")
            for q_hash, bank_item in test_bank.items():
                try:
                    # 获取题库中的题目
                    bank_question = bank_item.get('question', '')
                    if not bank_question:
                        continue

                    # 提取题库题目的内容
                    bank_content = extract_title(bank_question)
                    
                    # 计算相似度
                    similarity_score = fuzz.ratio(question_content.lower(), bank_content.lower())
                    
                    # 只显示相似度较高的匹配结果
                    if similarity_score > 30:
                        msg_manager.add_message(f"\n对比题库题目:", "info")
                        msg_manager.add_message(f"题库题目: {bank_content}", "info")
                        msg_manager.add_message(f"相似度: {similarity_score}%", "info")
                        msg_manager.add_message(f"题库答案: {bank_item.get('answer', '')}", "info")

                    # 更新最佳匹配
                    if similarity_score > best_score:
                        best_score = similarity_score
                        best_match = bank_item
                        msg_manager.add_message(f"↑ 发现新的最佳匹配！", "success")

                except Exception as e:
                    msg_manager.add_message(f"匹配过程出错: {str(e)}", "error")
                    continue

            # 显示本题最终匹配结果
            msg_manager.add_message("\n本题最终匹配结果:", "info")
            if best_match and best_score >= 80:
                msg_manager.add_message(f"匹配成功！最佳匹配分数: {best_score}%", "success")
                msg_manager.add_message(f"最终答案: {best_match.get('answer', '')}", "success")
            else:
                msg_manager.add_message(f"未找到合适的匹配 (最高分数: {best_score}%)", "warning")
            msg_manager.add_message("=" * 50, "info")

            # 记录匹配结果
            result = {
                "题号": i,
                "原始题目": test_question,
                "提取的内容": question_content,
                "最佳匹配分数": best_score,
                "匹配答案": best_match.get('answer', '未找到匹配') if best_match and best_score >= 80 else "未找到匹配"
            }
            results.append(result)
            
            # 显示本题匹配结果
            msg_manager.add_message("\n本题匹配结果:", "info")
            if best_match and best_score >= 80:
                msg_manager.add_message(f"匹配成功！最佳匹配分数: {best_score}", "success")
                msg_manager.add_message(f"匹配答案: {best_match.get('answer', '未找到匹配')}", "success")
            else:
                msg_manager.add_message("未找到合适的匹配", "warning")
            msg_manager.add_message("-" * 50, "info")

        msg_manager.add_message(f"\n=== 完成 {num_questions} 道题目的测试 ===", "info")
        return results

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return f"测试失败: {str(e)}"

# 新增：提取选项的函数
def extract_options(text):
    """提取题目中的选项"""
    options = []
    lines = text.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'^[A-D]\.', line):
            option = re.sub(r'^[A-D]\.', '', line).strip()
            options.append(option)
    return options

# 新：比较选项集合的相似度
def compare_options(options1, options2):
    """比较两组选项的相似度"""
    try:
        if not options1 or not options2:
            return 0
            
        if len(options1) != len(options2):
            return 0  # 选项数量不同，直接返回0
            
        # 计算选项之间的最大匹配分数
        total_score = 0
        matched_count = 0
        
        for opt1 in options1:
            best_opt_score = 0
            for opt2 in options2:
                try:
                    # 清理选项文本
                    cleaned_opt1 = clean_text(opt1)
                    cleaned_opt2 = clean_text(opt2)
                    
                    # 使用多种匹配方法
                    ratio_score = fuzz.ratio(cleaned_opt1, cleaned_opt2)
                    partial_score = fuzz.partial_ratio(cleaned_opt1, cleaned_opt2)
                    token_sort_score = fuzz.token_sort_ratio(cleaned_opt1, cleaned_opt2)
                    
                    # 取最高分数
                    score = max(ratio_score, partial_score, token_sort_score)
                    best_opt_score = max(best_opt_score, score)
                except:
                    continue
                    
            if best_opt_score >= 50:  # 只计算高于50分的匹配
                total_score += best_opt_score
                matched_count += 1
        
        # 返回平均分数，但要求至少有一半的选项匹配成功
        if matched_count >= len(options1) / 2:
            return total_score / len(options1)
        return 0
        
    except Exception as e:
        print(f"比较选项时出错: {e}")
        return 0

# 程序 (GUI 界)
def main():
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("题目与答案匹配工具")
        root.geometry("800x500")
        
        # 设置窗口样式
        style = ttk.Style()
        style.configure('TButton', font=('Arial', 10))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        
        # 创建左右分隔面板
        paned_window = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 左侧面板 - 控制区域
        left_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(left_frame, weight=1)
        
        # 右侧面板 - 消息区域
        right_frame = ttk.Frame(paned_window, padding="10")
        paned_window.add(right_frame, weight=2)
        
        # 右侧消息框
        message_frame = ttk.LabelFrame(right_frame, text="消息记录", padding="5")
        message_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建消息框顶部的工具栏
        toolbar_frame = ttk.Frame(message_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 添加清除按钮
        clear_button = ttk.Button(
            toolbar_frame, 
            text="清除消息", 
            command=lambda: msg_manager.clear_messages(),
            width=15
        )
        clear_button.pack(side=tk.RIGHT, padx=5)
        
        # 创建消息文本框和滚动条
        message_text = tk.Text(message_frame, wrap=tk.WORD, font=('Arial', 10))
        message_scrollbar = ttk.Scrollbar(message_frame, orient="vertical", command=message_text.yview)
        message_text.configure(yscrollcommand=message_scrollbar.set)
        
        message_text.pack(side="left", fill="both", expand=True)
        message_scrollbar.pack(side="right", fill="y")
        
        # 创建消息管理器
        msg_manager = MessageManager(message_text, root)
        
        # 定义功能函数
        def create_or_update_question_bank():
            try:
                reference_file = filedialog.askopenfilename(title='选择题库文件', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
                if reference_file:
                    msg_manager.add_message(f"选择题库文件: {reference_file}")
                    success, message = process_reference_questions(reference_file)
                    if success:
                        msg_manager.add_message(message, "success")
                    else:
                        msg_manager.add_message(message, "error")
            except Exception as e:
                msg_manager.add_message(f"创建/更新题库时发生错误: {str(e)}", "error")

        def view_question_bank():
            try:
                question_bank = load_question_bank()
                if not question_bank:
                    msg_manager.add_message("题库文件不存在或格式错误，请先创建题库", "error")
                    return
                    
                msg_manager.add_message("正在加载题库内容...")
                content = json.dumps(question_bank, ensure_ascii=False, indent=4)
                
                # 创建新窗口显示题库内容
                view_window = tk.Toplevel(root)
                view_window.title("题库内容")
                view_window.geometry("600x400")
                
                text_widget = tk.Text(view_window, wrap=tk.WORD, font=('Arial', 10))
                scrollbar = ttk.Scrollbar(view_window, orient="vertical", command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)
                
                text_widget.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")
                
                text_widget.insert("1.0", content)
                text_widget.configure(state='disabled')
                msg_manager.add_message("题库内容已加载成", "success")
            except Exception as e:
                msg_manager.add_message(f"查看题库时发生错误: {str(e)}", "error")

        def match_questions():
            try:
                question_bank = load_question_bank()
                if not question_bank:
                    msg_manager.add_message("题库为空或无法加载，请先创建题库", "error")
                    return
                
                questions_file = filedialog.askopenfilename(title='选择考试题目文', filetypes=[("Text files", "*.txt"), ("Word files", "*.docx")])
                if questions_file:
                    msg_manager.add_message(f"选择考试题目文件: {questions_file}")
                    questions = load_questions(questions_file)
                    if not questions:
                        msg_manager.add_message("未能读取到任何题目", "warning")
                        return
                    
                    msg_manager.add_message("正在匹配题目...")
                    output_file = filedialog.asksaveasfilename(title='保存结果文件', defaultextension=".docx", filetypes=[("Word files", "*.docx")])
                    if output_file:
                        msg_manager.add_message(f"开始处理题目，结果将保存到: {output_file}")
                        success, message = process_questions_one_by_one(questions, question_bank, output_file)
                        if success:
                            msg_manager.add_message(message, "success")
                        else:
                            msg_manager.add_message(message, "error")
            except Exception as e:
                msg_manager.add_message(f"匹配题目时发生错误: {str(e)}", "error")

        def close_app():
            root.destroy()

        def run_test_with_num():
            try:
                num_questions = int(test_num_var.get())
                if num_questions < 1 or num_questions > 100:
                    msg_manager.add_message("请输入1-100之间的数字", "warning")
                    return
                    
                msg_manager.add_message(f"开始测试 {num_questions} 道题目...", "info")
                results = test_single_match(msg_manager, num_questions)
                if isinstance(results, list):
                    msg_manager.add_message("\n=== 测试结果 ===", "info")
                    for result in results:
                        msg_manager.add_message(f"\n测试第 {result['题号']} 题:", "info")
                        for key, value in result.items():
                            if key != '题号':
                                msg_manager.add_message(f"{key}: {value}", "info")
                        msg_manager.add_message("-" * 50, "info")
                else:
                    msg_manager.add_message(str(results), "error")
            except ValueError:
                msg_manager.add_message("请输入有效的数字", "error")
            except Exception as e:
                msg_manager.add_message(f"测试时发生错误: {str(e)}", "error")

        # 左侧内容
        header = ttk.Label(left_frame, text="题目与答案匹配工具", style='Header.TLabel')
        header.pack(pady=(0, 20))
        
        # 创建测试题目数量输入框架
        test_frame = ttk.LabelFrame(left_frame, text="测试设置", padding="5")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建输入框和标签
        input_frame = ttk.Frame(test_frame)
        input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(input_frame, text="测试题目数量:").pack(side=tk.LEFT, padx=(0, 5))
        test_num_var = tk.StringVar(value="6")  # 默值为6
        test_num_entry = ttk.Entry(input_frame, textvariable=test_num_var, width=10)
        test_num_entry.pack(side=tk.LEFT)
        
        def validate_number(P):
            if P == "":
                return True
            try:
                num = int(P)
                return num > 0 and num <= 100  # 限制范围在1-100之间
            except ValueError:
                return False
        
        vcmd = (root.register(validate_number), '%P')
        test_num_entry.configure(validate='key', validatecommand=vcmd)
        
        # 创建按钮框架
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.BOTH, expand=True)
        
        # 定义按钮样式
        button_style = {'width': 20, 'padding': 10}
        
        # 添加按钮
        ttk.Button(button_frame, text="创建/更新题库", command=create_or_update_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="查看题库", command=view_question_bank, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="匹配题目", command=match_questions, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="测试指定数量题目", command=run_test_with_num, **button_style).pack(pady=10)
        ttk.Button(button_frame, text="关闭程", command=close_app, **button_style).pack(pady=10)
        
        # 添加初始消息
        msg_manager.add_message("程序已启动，等待操作...")
        
        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        root.mainloop()

    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        logging.error(f"程序运行时发生错误: {e}")

if __name__ == "__main__":
    main()
