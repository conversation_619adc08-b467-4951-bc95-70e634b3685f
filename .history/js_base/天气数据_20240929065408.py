'''
Author: hgxszhj <EMAIL>
Date: 2024-09-28 19:15:06
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2024-09-29 06:54:08
FilePath: /my_project/js_base/天气数据.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import requests
import execjs
from pprint import pprint
import pandas as pd
import json

#year = input('请输入查询的年份:')
#month = input('请输入查询的月份：')
js_files = open('/Users/<USER>/my_project/js_base/tq.js', encoding='utf-8').read()
js_data = execjs.compile(js_files)
m0fhOhhGL = 'GETDAYDATA'
info = {'city': '北京', 'month': '201402'}
hA4Nse2cT = js_data.call('Get_Data', m0fhOhhGL, info)
print(hA4Nse2cT)
headers = {
        "Referer": "https://www.aqistudy.cn/historydata/daydata.php?city=%E5%8C%97%E4%BA%AC&month=201409",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
cookies = {
        "Hm_lvt_6088e7f72f5a363447d4bafe03026db8": "**********",
        "HMACCOUNT": "3365371431026BE3",
        "Hm_lpvt_6088e7f72f5a363447d4bafe03026db8": "**********"
}
url = "https://www.aqistudy.cn/historydata/api/historyapi.php"
data = {
    "hA4Nse2cT": hA4Nse2cT
}
response = requests.post(url=url, headers=headers, cookies=cookies, data=data)
print(response.text)  # 检查响应的文本内容
json_data = js_data.call('dxvERkeEvHbS', response.text)
pprint(json_data)  # 检查 json_data 的内容
if not json_data:
    print("Received empty JSON data")
    exit()  # 选择退出或提供默认值
else:
    json_data = json.loads(json_data)  # 解析 JSON 字符串
    items = json_data['result']['data']['items']
df = pd.DataFrame(items)
df.to_excel(f'.tmp\\123.xlsx', index=False)
