'''
Author: hgxszhj <EMAIL>
Date: 2024-09-28 19:15:06
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2024-10-22 17:55:21
FilePath: /my_project/js_base/天气数据.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import requests
import execjs
from pprint import pprint
import pandas as pd
import json

#year = input('请输入查询的年份:')
#month = input('请输入查询的月份：')
js_files = open('/Users/<USER>/my_project/js_base/tq.js', encoding='utf-8').read()
js_data = execjs.compile(js_files)
m0fhOhhGL = 'GETDAYDATA'
info = {'city': '北京', 'month': '201402'}
hA4Nse2cT = js_data.call('Get_Data', m0fhOhhGL, info)
print(hA4Nse2cT)
'''
Author: hgxszhj <EMAIL>
Date: 2024-09-28 19:15:06
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2024-09-29 06:54:08
FilePath: /my_project/js_base/天气数据.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import requests
import execjs
from pprint import pprint
import pandas as pd
import json
import os  # 导入os模块以处理文件路径

# 读取JS文件并编译
js_file_path = '/Users/<USER>/my_project/js_base/tq.js'
try:
    with open(js_file_path, encoding='utf-8') as js_file:
        js_data = execjs.compile(js_file.read())
except FileNotFoundError:
    print(f"文件未找到: {js_file_path}")
    exit()
except Exception as e:
    print(f"读取文件时发生错误: {e}")
    exit()

# 设置请求参数
m0fhOhhGL = 'GETDAYDATA'
info = {'city': '北京', 'month': '201402'}

# 获取hA4Nse2cT
try:
    hA4Nse2cT = js_data.call('Get_Data', m0fhOhhGL, info)
    print(hA4Nse2cT)
except Exception as e:
    print(f"JS调用失败: {e}")
    exit()

# 设置请求头和Cookies
headers = {
    "Referer": "https://www.aqistudy.cn/historydata/daydata.php?city=%E5%8C%97%E4%BA%AC&month=201409",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
}
cookies = {
    "Hm_lvt_6088e7f72f5a363447d4bafe03026db8": "**********",
    "HMACCOUNT": "3365371431026BE3",
    "Hm_lpvt_6088e7f72f5a363447d4bafe03026db8": "**********"
}

# 发送POST请求
url = "https://www.aqistudy.cn/historydata/api/historyapi.php"
data = {"hA4Nse2cT": hA4Nse2cT}
try:
    response = requests.post(url=url, headers=headers, cookies=cookies, data=data)
    response.raise_for_status()  # 如果响应状态码不是200，将引发HTTPError
except requests.RequestException as e:
    print(f"请求失败: {e}")
    exit()

# 打印响应内容
print(response.text)

# 处理响应数据
try:
    json_data = js_data.call('dxvERkeEvHbS', response.text)
    pprint(json_data)  # 检查 json_data 的内容
    if not json_data:
        raise ValueError("Received empty JSON data")

    json_data = json.loads(json_data)  # 解析 JSON 字符串
    items = json_data['result']['data']['items']
    if not items:
        raise ValueError("No items found in JSON data")
except ValueError as e:
    print(e)
    exit()
except json.JSONDecodeError:
    print("JSON解析失败")
    exit()
except Exception as e:
    print(f"处理数据时发生错误: {e}")
    exit()

# 保存数据到Excel
output_dir = '.tmp'
os.makedirs(output_dir, exist_ok=True)  # 创建输出目录（如果不存在）
output_file = os.path.join(output_dir, '123.xlsx')
df = pd.DataFrame(items)
df.to_excel(output_file, index=False)
print(f"数据已保存到 {output_file}")

headers = {
        "Referer": "https://www.aqistudy.cn/historydata/daydata.php?city=%E5%8C%97%E4%BA%AC&month=201409",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
}
cookies = {
        "Hm_lvt_6088e7f72f5a363447d4bafe03026db8": "**********",
        "HMACCOUNT": "3365371431026BE3",
        "Hm_lpvt_6088e7f72f5a363447d4bafe03026db8": "**********"
}
url = "https://www.aqistudy.cn/historydata/api/historyapi.php"
data = {
    "hA4Nse2cT": hA4Nse2cT
}
response = requests.post(url=url, headers=headers, cookies=cookies, data=data)
print(response.text)  # 检查响应的文本内容
json_data = js_data.call('dxvERkeEvHbS', response.text)
pprint(json_data)  # 检查 json_data 的内容
if not json_data:
    print("Received empty JSON data")
    exit()  # 选择退出或提供默认值
else:
    json_data = json.loads(json_data)  # 解析 JSON 字符串
    items = json_data['result']['data']['items']
df = pd.DataFrame(items)
df.to_excel(f'.tmp\\123.xlsx', index=False)
