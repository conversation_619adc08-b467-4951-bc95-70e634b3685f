import requests
import execjs
from pprint import pprint
import pandas as pd
import json

#year = input('请输入查询的年份:')
#month = input('请输入查询的月份：')
js_files = open('/Users/<USER>/my_project/js_base/tq.js', encoding='utf-8').read()
js_data = execjs.compile(js_files)
m0fhOhhGL = 'GETDAYDATA'
info = {'city': '北京', 'month': '201402'}
hA4Nse2cT = js_data.call('Get_Data', m0fhOhhGL, info)
print(hA4Nse2cT)
headers = {
        "Referer": "https://www.aqistudy.cn/historydata/daydata.php?city=%E5%8C%97%E4%BA%AC&month=201409",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
}
cookies = {
        "Hm_lvt_6088e7f72f5a363447d4bafe03026db8": "**********",
        "HMACCOUNT": "3365371431026BE3",
        "Hm_lpvt_6088e7f72f5a363447d4bafe03026db8": "**********"
}
url = "https://www.aqistudy.cn/historydata/api/historyapi.php"
data = {
    "hA4Nse2cT": hA4Nse2cT
}
response = requests.post(url=url, headers=headers, cookies=cookies, data=data)
print(response)
json_data = js_data.call('dxvERkeEvHbS', response.text)
print(json_data)
items = json_data['result']['data']['items']
df = pd.DataFrame(items)
df.to_excel(f'.tmp\\123.xlsx', index=False)
