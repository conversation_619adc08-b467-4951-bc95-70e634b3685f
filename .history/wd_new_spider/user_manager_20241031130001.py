import json
import hashlib
import os
from datetime import datetime

class UserManager:
    def __init__(self, db_file="users.json"):
        self.db_file = db_file
        self.users = self.load_users()
        
        # 确保root用户存在
        if not self.get_user("root"):
            self.add_root_user()
    
    def load_users(self):
        """加载用户数据"""
        if os.path.exists(self.db_file):
            try:
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}
    
    def save_users(self):
        """保存用户数据"""
        with open(self.db_file, 'w', encoding='utf-8') as f:
            json.dump(self.users, f, indent=4, ensure_ascii=False)
    
    def add_root_user(self):
        """添加root用户"""
        root_user = {
            "username": "root",
            "password": self.hash_password("toor"),
            "is_admin": True,
            "is_active": True,
            "permissions": ["all"],  # root用户拥有所有权限
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        self.users["root"] = root_user
        self.save_users()
    
    def hash_password(self, password):
        """密码加密"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def get_user(self, username):
        """获取用户信息"""
        return self.users.get(username)
    
    def verify_password(self, username, password):
        """验证密码"""
        user = self.get_user(username)
        if user:
            return user["password"] == self.hash_password(password)
        return False
    
    def register_user(self, username, password):
        """注册新用户"""
        if username in self.users:
            return False, "用户名已存在"
        
        new_user = {
            "username": username,
            "password": self.hash_password(password),
            "is_admin": False,
            "is_active": False,  # 需要管理员激活
            "permissions": [],  # 初始没有任何权限
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        self.users[username] = new_user
        self.save_users()
        return True, "注册成功，等待管理员授权"
    
    def activate_user(self, admin_username, admin_password, target_username):
        """管理员激活用户"""
        # 验证管理员身份
        if not self.verify_password(admin_username, admin_password):
            return False, "管理员验证失败"
        
        admin_user = self.get_user(admin_username)
        if not admin_user or not admin_user["is_admin"]:
            return False, "需要管理员权限"
        
        target_user = self.get_user(target_username)
        if not target_user:
            return False, "用户不存在"
        
        target_user["is_active"] = True
        self.save_users()
        return True, "用户已激活"
    
    def update_user_permissions(self, admin_username, admin_password, target_username, permissions):
        """更新用户权限"""
        # 验证管理员身份
        if not self.verify_password(admin_username, admin_password):
            return False, "管理员验证失败"
        
        admin_user = self.get_user(admin_username)
        if not admin_user or not admin_user["is_admin"]:
            return False, "需要管理员权限"
        
        target_user = self.get_user(target_username)
        if not target_user:
            return False, "用户不存在"
        
        target_user["permissions"] = permissions
        self.save_users()
        return True, "用户权限已更新"
    
    def get_user_permissions(self, username):
        """获取用户权限"""
        user = self.get_user(username)
        if user:
            return user.get("permissions", [])
        return []
    
    def has_permission(self, username, permission):
        """检查用户是否有特定权限"""
        user = self.get_user(username)
        if not user:
            return False
        if user["is_admin"]:  # 管理员拥有所有权限
            return True
        return permission in user.get("permissions", []) 