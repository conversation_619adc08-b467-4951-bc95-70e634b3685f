import requests
from pprint import pprint
import urllib3
import os
from tqdm import tqdm
import logging
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from queue import Queue
from threading import Thread

# 禁用 InsecureRequestWarning 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF文件下载器")
        self.root.geometry("900x600")
        
        # 添加配置文件的默认路径
        self.config_file = "pdf_downloader_config.json"
        
        # 初始化主题样式
        self.style = ttk.Style()
        self.available_themes = self.style.theme_names()
        self.current_theme = tk.StringVar(value=self.style.theme_use())
        
        # 创建所有框架
        self._create_frames()
        
        # 创建所有界面组件
        self.create_menu_bar()
        self.create_left_panel()
        self.create_right_panel()
        self.create_bottom_panel()
        
        # 尝试加载已保存的配置
        self.load_config()

    def _create_frames(self):
        """创建所有主要框架"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分栏
        self.left_frame = ttk.LabelFrame(self.main_frame, text="参数设置", padding="10")
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 5))
        
        self.right_frame = ttk.LabelFrame(self.main_frame, text="处理日志", padding="10")
        self.right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 创建底部框架
        self.bottom_frame = ttk.Frame(self.root, padding="10")
        self.bottom_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        # 创建日志文本框
        self.log_text = tk.Text(self.right_frame, height=20, width=50)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)

    def browse_save_path(self):
        """打开文件夹选择对话框"""
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.save_path_entry.delete(0, tk.END)
            self.save_path_entry.insert(0, folder_path)

    def validate_save_path(self, path):
        """验证保存路径"""
        if not path:
            self.log_message("错误: 请选择保存路径")
            return False
        if not os.path.exists(path):
            try:
                os.makedirs(path)
            except Exception as e:
                self.log_message(f"错误: 创建保存路径失败 - {str(e)}")
                return False
        return True

    def create_left_panel(self):
        # Authorization输入
        ttk.Label(self.left_frame, text="Authorization:").pack(anchor=tk.W, pady=(0, 2))
        self.auth_entry = ttk.Entry(self.left_frame, width=40)
        self.auth_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Cookie输入
        ttk.Label(self.left_frame, text="Cookie:").pack(anchor=tk.W, pady=(0, 2))
        self.cookie_entry = ttk.Entry(self.left_frame, width=40)
        self.cookie_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 基础URL输入
        ttk.Label(self.left_frame, text="基础URL:").pack(anchor=tk.W, pady=(0, 2))
        self.base_url_entry = ttk.Entry(self.left_frame, width=40)
        self.base_url_entry.insert(0, "https://kc.zhixueyun.com")
        self.base_url_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 文件ID输入
        ttk.Label(self.left_frame, text="文件ID:").pack(anchor=tk.W, pady=(0, 2))
        self.file_id_entry = ttk.Entry(self.left_frame, width=40)
        self.file_id_entry.pack(fill=tk.X, pady=(0, 10))
        
        # _值输入
        ttk.Label(self.left_frame, text="_ 值:").pack(anchor=tk.W, pady=(0, 2))
        self.timestamp_entry = ttk.Entry(self.left_frame, width=40)
        self.timestamp_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 保存路径选择
        ttk.Label(self.left_frame, text="保存路径:").pack(anchor=tk.W, pady=(0, 2))
        save_path_frame = ttk.Frame(self.left_frame)
        save_path_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.save_path_entry = ttk.Entry(save_path_frame)
        self.save_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.browse_button = ttk.Button(save_path_frame, text="浏览", command=self.browse_save_path)
        self.browse_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 创建按钮框架
        button_frame = ttk.Frame(self.left_frame)
        button_frame.pack(pady=(20, 0))
        
        # 添加开始下载按钮
        self.download_button = ttk.Button(button_frame, text="开始下载", command=self.start_download)
        self.download_button.pack(side=tk.LEFT, padx=5)
        
        # 添加保存配置按钮
        self.save_config_button = ttk.Button(button_frame, text="保存配置", command=self.save_config)
        self.save_config_button.pack(side=tk.LEFT, padx=5)

    def create_right_panel(self):
        # 创建notebook用于切换日志和任务列表
        self.notebook = ttk.Notebook(self.right_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 日志页面
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="日志")
        
        # 任务列表页面
        self.task_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.task_frame, text="下载任务")
        
        # 创建任务列表
        self.task_tree = ttk.Treeview(self.task_frame, columns=("文件ID", "状态", "进度"))
        self.task_tree.heading("文件ID", text="文件ID")
        self.task_tree.heading("状态", text="状态")
        self.task_tree.heading("进度", text="进度")
        self.task_tree.pack(fill=tk.BOTH, expand=True)

    def create_bottom_panel(self):
        # 状态标签
        self.status_label = ttk.Label(self.bottom_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(self.bottom_frame, orient="horizontal", 
                                      length=400, mode="determinate")
        self.progress.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

    def log_message(self, message):
        """向日志文本框添加消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def start_download(self):
        # 清空日志
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 获取输入值
        headers = {
            "Accept": "*/*",
            "Accept-Language": "zh,zh-CN;q=0.9,en;q=0.8",
            "Authorization": self.auth_entry.get(),
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Cookie": self.cookie_entry.get(),
            "Referer": "https://kc.zhixueyun.com/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
        }
        
        base_url = self.base_url_entry.get()
        file_id = self.file_id_entry.get()
        timestamp = self.timestamp_entry.get()
        save_path = self.save_path_entry.get()

        # 验证输入
        if not all([headers["Authorization"], headers["Cookie"], base_url, file_id, timestamp]):
            self.log_message("错误: 请填写所有必要的信息")
            return

        # 验证保存路径
        if not self.validate_save_path(save_path):
            return

        # 创建下载器实例
        downloader = PDFDownloader(headers, base_url)
        
        try:
            # 获取文件信息
            self.status_label["text"] = "正在获取文件信息..."
            self.log_message("正在获取文件信息...")
            file_url, filename = downloader.fetch_file_info(file_id, timestamp)
            
            if file_url and filename:
                output_filename = os.path.join(save_path, filename)
                
                # 检查文件是否已存在
                if os.path.exists(output_filename):
                    if not messagebox.askyesno("文件已存在", 
                        f"文件 {filename} 已存在，是否覆盖？"):
                        self.log_message("下载已取消")
                        return
                
                self.status_label["text"] = f"正在下载: {filename}"
                self.log_message(f"开始下载文件: {filename}")
                
                # 下载文件
                downloader.download_pdf(file_url, output_filename, self.update_progress)
                
                self.status_label["text"] = "下载完成！"
                self.log_message(f"文件已保存至: {output_filename}")
            else:
                self.status_label["text"] = "获取文件信息失败"
                self.log_message("错误: 无法获取文件信息")
        except Exception as e:
            self.status_label["text"] = "下载过程中出现错误"
            self.log_message(f"错误: {str(e)}")
        finally:
            self.progress["value"] = 0

    def update_progress(self, current, total):
        progress = (current / total) * 100
        self.progress["value"] = progress
        self.status_label["text"] = f"下载进度: {progress:.1f}%"
        self.root.update_idletasks()

    def save_config(self):
        """保存当前配置到文件（更新版本）"""
        config = {
            'authorization': self.auth_entry.get(),
            'cookie': self.cookie_entry.get(),
            'base_url': self.base_url_entry.get(),
            'save_path': self.save_path_entry.get(),
            'theme': self.current_theme.get()  # ��存主题设置
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.log_message("配置已成功保存")
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载配置文件（更新版本）"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 设置已保存的值
                self.auth_entry.delete(0, tk.END)
                self.auth_entry.insert(0, config.get('authorization', ''))
                
                self.cookie_entry.delete(0, tk.END)
                self.cookie_entry.insert(0, config.get('cookie', ''))
                
                self.base_url_entry.delete(0, tk.END)
                self.base_url_entry.insert(0, config.get('base_url', 'https://kc.zhixueyun.com'))
                
                self.save_path_entry.delete(0, tk.END)
                self.save_path_entry.insert(0, config.get('save_path', ''))
                
                # 加载主题设置
                saved_theme = config.get('theme')
                if saved_theme and saved_theme in self.available_themes:
                    self.style.theme_use(saved_theme)
                    self.current_theme.set(saved_theme)
                
                self.log_message("配置已加载")
        except Exception as e:
            self.log_message(f"加载配置失败: {str(e)}")

    def create_menu_bar(self):
        """创建顶部菜单栏"""
        # 创建菜单栏框架
        menu_frame = ttk.Frame(self.root)
        menu_frame.pack(fill=tk.X, padx=10, pady=(5,0))
        
        # 创建主题选择区域
        ttk.Label(menu_frame, text="主题:").pack(side=tk.LEFT, padx=(0,5))
        theme_combobox = ttk.Combobox(
            menu_frame, 
            textvariable=self.current_theme,
            values=self.available_themes,
            state="readonly",
            width=15
        )
        theme_combobox.pack(side=tk.LEFT)
        
        # 绑定主题切换事件
        theme_combobox.bind('<<ComboboxSelected>>', self.change_theme)
        
        # 添加设置按钮
        settings_button = ttk.Button(
            menu_frame, 
            text="设置",
            command=self.open_settings
        )
        settings_button.pack(side=tk.RIGHT)

    def change_theme(self, event=None):
        """切换主题"""
        new_theme = self.current_theme.get()
        try:
            self.style.theme_use(new_theme)
            self.save_theme_preference(new_theme)
            self.log_message(f"主题已切换为: {new_theme}")
        except Exception as e:
            self.log_message(f"切换主题失败: {str(e)}")

    def save_theme_preference(self, theme):
        """保存主题偏好到配置文件"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            config['theme'] = theme
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logger.error(f"保存主题偏好失败: {e}")

    def open_settings(self):
        """打开设置对话框"""
        settings = SettingsDialog(self.root, self.config_file)

class DownloadError(Exception):
    """自定义下载异常类"""
    pass

class PDFDownloader:
    def __init__(self, headers, base_url):
        self.headers = headers
        self.base_url = base_url

    def fetch_file_info(self, file_id, timestamp):
        """获取文件信息，返回文件下载的路径 URL 和文件名。"""
        url = f"{self.base_url}/api/v1/tools-center-v2/file-cloud/preview"
        params = {
            "id": file_id,
            "_": timestamp
        }
        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False, timeout=10)
            response.raise_for_status()
            data = response.json()
            if not isinstance(data, dict):
                raise DownloadError("响应格式无效，未能获取文件信息")
            file_url = data.get("url")
            filename = data.get("filename")
            if not file_url or not filename:
                raise DownloadError("文件信息不完整，请检查文件 ID 是否正确")
            return file_url, filename
        except requests.Timeout:
            raise DownloadError("请求超时，请检查网络连接")
        except requests.RequestException as e:
            raise DownloadError(f"请求文件信息失败: {str(e)}")

    def download_pdf(self, file_url, output_filename, progress_callback=None):
        """下载 PDF 文件并保存到指定路径。"""
        full_url = f"{self.base_url}{file_url}"
        try:
            response = requests.get(full_url, headers=self.headers, verify=False, stream=True)
            response.raise_for_status()
            total_size = int(response.headers.get('content-length', 0))
            current_size = 0
            
            with open(output_filename, "wb") as f:
                for data in response.iter_content(chunk_size=1024):
                    f.write(data)
                    current_size += len(data)
                    if progress_callback:
                        progress_callback(current_size, total_size)
                        
            logger.info(f"文件已保存为 {output_filename}")
        except requests.exceptions.RequestException as e:
            logger.error(f"PDF 文件下载失败: {e}")
            raise

class ConfigManager:
    def __init__(self, config_file):
        self.config_file = config_file
        self.default_config = {
            'authorization': '',
            'cookie': '',
            'base_url': 'https://kc.zhixueyun.com',
            'save_path': ''
        }

    def load(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return {**self.default_config, **json.load(f)}
            return self.default_config.copy()
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return self.default_config.copy()

    def save(self, config):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False

class DownloadTask:
    def __init__(self, file_id, timestamp, output_path):
        self.file_id = file_id
        self.timestamp = timestamp
        self.output_path = output_path
        self.status = "pending"
        self.progress = 0

class DownloadManager:
    def __init__(self, downloader):
        self.downloader = downloader
        self.task_queue = Queue()
        self.tasks = {}
        self._running = False
        self._worker = None

    def add_task(self, task):
        """添加下载任务"""
        self.task_queue.put(task)
        self.tasks[task.file_id] = task
        if not self._running:
            self._start_worker()

    def _start_worker(self):
        """启动工作线程"""
        self._running = True
        self._worker = Thread(target=self._process_queue, daemon=True)
        self._worker.start()

    def _process_queue(self):
        """处理下载队列"""
        while self._running:
            try:
                task = self.task_queue.get(timeout=1)
                self._process_task(task)
                self.task_queue.task_done()
            except Queue.Empty:
                continue

    def _process_task(self, task):
        """处理单个下载任务"""
        try:
            task.status = "downloading"
            file_url, filename = self.downloader.fetch_file_info(task.file_id, task.timestamp)
            if file_url and filename:
                output_path = os.path.join(task.output_path, filename)
                self.downloader.download_pdf(file_url, output_path, 
                                          lambda c, t: self._update_progress(task, c, t))
                task.status = "completed"
            else:
                task.status = "failed"
        except Exception as e:
            task.status = "failed"
            logger.error(f"下载任务失败: {str(e)}")

    def _update_progress(self, task, current, total):
        """更新下载进度"""
        task.progress = (current / total) * 100

class SettingsDialog:
    def __init__(self, parent, config):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("设置")
        self.dialog.geometry("400x300")
        self.config = config
        
        # 创建设置选项
        self.create_settings_ui()
        
    def create_settings_ui(self):
        # 代理设置
        proxy_frame = ttk.LabelFrame(self.dialog, text="代理设置", padding="5")
        proxy_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(proxy_frame, text="HTTP代理:").pack(anchor=tk.W)
        self.proxy_entry = ttk.Entry(proxy_frame)
        self.proxy_entry.pack(fill=tk.X)
        
        # 下载设置
        download_frame = ttk.LabelFrame(self.dialog, text="下载设置", padding="5")
        download_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(download_frame, text="并发下载数:").pack(anchor=tk.W)
        self.concurrent_spinbox = ttk.Spinbox(download_frame, from_=1, to=5)
        self.concurrent_spinbox.pack(fill=tk.X)

def main():
    root = tk.Tk()
    app = PDFDownloaderGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
