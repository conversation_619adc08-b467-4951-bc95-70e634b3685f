LANGUAGES = {
    "中文": {
        "title": "电信网上大学PDF文件下载器",
        "settings": "参数设置",
        "log": "处理日志",
        "download_tasks": "下载任务",
        "authorization": "Authorization:",
        "cookie": "Cookie:",
        "base_url": "基础URL:",
        "file_id": "文件ID:",
        "timestamp": "_ 值:",
        "save_path": "保存路径:",
        "browse": "浏览",
        "start_download": "开始下载",
        "save_config": "保存配置",
        "theme_settings": "主题设置:",
        "apply_theme": "应用主题",
        "settings_btn": "设置",
        "ready": "就绪",
        "downloading": "正在下载",
        "download_complete": "下载完成！",
        "download_failed": "下载失败",
        "file_exists": "文件已存在",
        "overwrite_confirm": "文件 {} 已存在，是否覆盖？",
        "config_saved": "配置已保存",
        "config_loaded": "配置已加载",
        "error_prefix": "错误: ",
        "success": "成功",
        "error": "错误",
        "theme_changed": "主题已切换为: {}",
        "proxy_settings": "代理设置",
        "http_proxy": "HTTP代理:",
        "download_settings": "下载设置",
        "concurrent_downloads": "并发下载数:",
        "clear_log": "清除日志",
        "export_log": "导出日志",
        "log_cleared": "日志已清除",
        "export_log_title": "导出日志",
        "log_exported": "日志已导出",
        "export_log_failed": "导出日志失败",
        "batch_download": "批量下载",
        "file_id_list": "文件ID列表（每行一个）",
        "start_batch_download": "开始批量下载",
        "cancel": "取消",
        "warning": "警告",
        "no_file_ids": "请输入至少一个文件ID",
        "history": "下载历史",
        "show_window": "显示窗口",
        "exit": "退出",
        "minimize_to_tray": "最小化到托盘",
        "minimize_to_tray_msg": "是否最小化到系统托盘？",
        "download_success": "下载成功",
        "download_failed_msg": "下载失败，请重试",
        "update_available": "有可用更新",
        "update_msg": "发现新版本 {}，是否更新？",
        "retry_download": "重试下载",
        "retry_count": "重试次数: {}/{}"
    },
    "English": {
        "title": "Telecom Online University PDF Downloader",
        "settings": "Settings",
        "log": "Processing Log",
        "download_tasks": "Download Tasks",
        "authorization": "Authorization:",
        "cookie": "Cookie:",
        "base_url": "Base URL:",
        "file_id": "File ID:",
        "timestamp": "Timestamp:",
        "save_path": "Save Path:",
        "browse": "Browse",
        "start_download": "Start Download",
        "save_config": "Save Config",
        "theme_settings": "Theme:",
        "apply_theme": "Apply Theme",
        "settings_btn": "Settings",
        "ready": "Ready",
        "downloading": "Downloading",
        "download_complete": "Download Complete!",
        "download_failed": "Download Failed",
        "file_exists": "File Exists",
        "overwrite_confirm": "File {} already exists, overwrite?",
        "config_saved": "Configuration Saved",
        "config_loaded": "Configuration Loaded",
        "error_prefix": "Error: ",
        "success": "Success",
        "error": "Error",
        "theme_changed": "Theme changed to: {}",
        "proxy_settings": "Proxy Settings",
        "http_proxy": "HTTP Proxy:",
        "download_settings": "Download Settings",
        "concurrent_downloads": "Concurrent Downloads:",
        "clear_log": "Clear Log",
        "export_log": "Export Log",
        "log_cleared": "Log Cleared",
        "export_log_title": "Export Log",
        "log_exported": "Log Exported",
        "export_log_failed": "Failed to Export Log",
        "batch_download": "Batch Download",
        "file_id_list": "File ID List (One per line)",
        "start_batch_download": "Start Batch Download",
        "cancel": "Cancel",
        "warning": "Warning",
        "no_file_ids": "Please enter at least one file ID",
        "history": "Download History",
        "show_window": "Show Window",
        "exit": "Exit",
        "minimize_to_tray": "Minimize to Tray",
        "minimize_to_tray_msg": "Minimize to system tray?",
        "download_success": "Download Success",
        "download_failed_msg": "Download failed, please retry",
        "update_available": "Update Available",
        "update_msg": "New version {} available. Update now?",
        "retry_download": "Retry Download",
        "retry_count": "Retry count: {}/{}"
    }
} 