'''
Author: hgxszhj <EMAIL>
Date: 2024-05-29 15:56:53
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2025-03-01 07:22:52
FilePath: /my_project/2024-5-20/download_myctu_v01.py
Description: File download utility with configurable settings and error handling
'''
import requests
import json
import base64
from pathlib import Path
import re
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from colorama import Fore, init
from typing import Dict, Any
import logging
import os
from dotenv import load_dotenv

init(autoreset=True)

# Load environment variables
load_dotenv()

DEBUG = False

HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Cookie": os.getenv("COOKIE", ""),
    "Authorization": os.getenv("AUTH_TOKEN", "")
}

def create_session():
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504]
    )
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

def print_status(message: str, status: str = "info"):
    """统一的状态输出函数"""
    colors = {
        "info": Fore.CYAN,
        "success": Fore.GREEN,
        "error": Fore.RED,
        "warning": Fore.YELLOW
    }
    print(f"{colors.get(status, Fore.WHITE)}{message}")

def download_file(url: str, file_path: str, headers: dict) -> bool:
    """返回布尔值表示下载是否成功"""
    try:
        session = create_session()
        print_status("正在下载文件...", "info")
        response = session.get(url, headers=headers, timeout=(3.05, 30))
        response.raise_for_status()
        
        data = base64.b64decode(response.json()['body'])
        
        with open(file_path, 'wb') as f:
            f.write(data)
        print_status(f"文件已成功保存至：{file_path}", "success")
        return True
        
    except requests.exceptions.RequestException as e:
        print_status(f"网络请求失败: {str(e)}", "error")
    except KeyError:
        print_status("响应数据格式异常，缺少'body'字段", "error")
    except IOError as e:
        print_status(f"文件保存失败: {str(e)}", "error")
    except Exception as e:
        print_status(f"发生未知错误: {str(e)}", "error")
    return False

def sanitize_filename(filename: str) -> str:
    # 移除非法字符并限制文件名长度
    cleaned = re.sub(r'[\\/*?:"<>|]', "", filename)[:100]
    return cleaned or "untitled"

def show_banner():
    print(Fore.CYAN + r"""
     ____  _             _          _   _             
    |  _ \| |_   _  __ _| |__      | | | |___  ___ _ __ 
    | | | | | | | |/ _` | '_ \  _  | | | / __|/ _ \ '__|
    | |_| | | |_| | (_| | | | || | | |_| \__ \  __/ |   
    |____/|_|\__,_|\__,_|_| |_(_)  \___/|___/\___|_|   
    """)

def toggle_debug():
    global DEBUG
    DEBUG = not DEBUG
    print_status(f"调试模式 {'已启用' if DEBUG else '已禁用'}", "warning")

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('download.log'),
            logging.StreamHandler()
        ]
    )

class Config:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config: Dict[str, Any] = self._load_config()
        self.debug = False

    def _load_config(self) -> Dict[str, Any]:
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        default_config = {
            "save_dir": "D:/EXAM/岗位认证",
            "headers": HEADERS,
            "debug": False
        }
        self.save_config(default_config)
        return default_config

    def save_config(self, config: Dict[str, Any]) -> None:
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=4)

    def toggle_debug(self):
        """Toggle debug mode"""
        self.debug = not self.debug
        print_status(f"调试模式 {'已启用' if self.debug else '已禁用'}", "warning")

def main():
    setup_logging()
    show_banner()
    config = Config()
    
    while True:
        try:
            url = input("请输入文件下载链接（输入'q'退出）：").strip()
            if url.lower() == 'q':
                break
                
            if not url.startswith(('http://', 'https://')):
                logging.error("链接格式不正确，必须以http://或https://开头")
                print_status("链接格式不正确，必须以http://或https://开头", "error")
                continue
            
            raw_name = input("请输入文件名：")
            safe_name = sanitize_filename(raw_name)
            
            save_dir = Path(config.config["save_dir"])
            save_dir.mkdir(parents=True, exist_ok=True)
            file_path = save_dir / f"{safe_name}.pdf"
            
            if file_path.exists():
                overwrite = input("文件已存在，是否覆盖？(y/n): ").lower()
                if overwrite != 'y':
                    print_status("已取消下载", "warning")
                    continue
            
            if config.debug:
                logging.debug(f"请求头：{HEADERS}")
                print_status(f"请求头：{HEADERS}", "info")
            
            if download_file(url, str(file_path), HEADERS):
                logging.info("下载完成！")
                print_status("下载完成！", "success")
            
        except KeyboardInterrupt:
            logging.warning("程序已终止")
            print_status("\n程序已终止", "warning")
            break
        except Exception as e:
            logging.error(f"发生错误: {str(e)}")
            print_status(f"发生错误: {str(e)}", "error")

if __name__ == '__main__':
    main()
