'''
Author: hgxszhj <EMAIL>
Date: 2024-05-29 15:56:53
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2025-03-01 08:56:15
FilePath: /my_project/2024-5-20/download_myctu_v01.py
Description: File download utility with configurable settings and error handling
'''
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, StringVar
import requests
import json
import base64
from pathlib import Path
import re
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from colorama import Fore, init
from typing import Dict, Any, Optional, Tuple, List
import logging
import os
from dotenv import load_dotenv
import threading
import time
import sys
from datetime import datetime

# Initialize colorama
init(autoreset=True)

# Load environment variables
load_dotenv()

# Constants
DEBUG = False
VERSION = "0.2.0"
DEFAULT_FILE_TYPE = ".pdf"
SUPPORTED_FILE_TYPES = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar"]
LOG_FILE = "download_logs.log"

# Default headers for HTTP requests
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Cookie": os.getenv("COOKIE", ""),
    "Authorization": os.getenv("AUTH_TOKEN", "")
}

def create_session() -> requests.Session:
    """
    Create and configure a requests session with retry capabilities.
    
    Returns:
        requests.Session: Configured session object
    """
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504]
    )
    session.mount('https://', HTTPAdapter(max_retries=retries))
    session.mount('http://', HTTPAdapter(max_retries=retries))
    return session

def print_status(message: str, status: str = "info") -> None:
    """
    Print a status message with color coding.
    
    Args:
        message (str): The message to print
        status (str): Status type (info, success, error, warning)
    """
    colors = {
        "info": Fore.CYAN,
        "success": Fore.GREEN,
        "error": Fore.RED,
        "warning": Fore.YELLOW
    }
    print(f"{colors.get(status, Fore.WHITE)}{message}")

def download_file(url: str, file_path: str, headers: dict, progress_callback=None) -> bool:
    """
    Download a file from a URL and save it to the specified path.
    
    Args:
        url (str): The URL to download from
        file_path (str): The path to save the file to
        headers (dict): HTTP headers to use for the request
        progress_callback (callable, optional): Callback function for progress updates
        
    Returns:
        bool: True if download was successful, False otherwise
    """
    try:
        session = create_session()
        print_status("正在下载文件...", "info")
        
        # Stream the response to handle progress
        with session.get(url, headers=headers, timeout=(3.05, 30), stream=True) as response:
            response.raise_for_status()
            
            # Get the data from the response
            data = base64.b64decode(response.json()['body'])
            total_size = len(data)
            
            with open(file_path, 'wb') as f:
                if progress_callback:
                    # Update progress as we write
                    chunk_size = total_size // 100 if total_size > 100 else 1
                    for i in range(0, total_size, chunk_size):
                        end = min(i + chunk_size, total_size)
                        f.write(data[i:end])
                        progress = (end / total_size) * 100
                        progress_callback(progress)
                        time.sleep(0.01)  # Small delay to allow UI updates
                else:
                    f.write(data)
                    
        print_status(f"文件已成功保存至：{file_path}", "success")
        return True
        
    except requests.exceptions.RequestException as e:
        print_status(f"网络请求失败: {str(e)}", "error")
    except KeyError:
        print_status("响应数据格式异常，缺少'body'字段", "error")
    except IOError as e:
        print_status(f"文件保存失败: {str(e)}", "error")
    except Exception as e:
        print_status(f"发生未知错误: {str(e)}", "error")
    return False

def sanitize_filename(filename: str) -> str:
    """
    Remove illegal characters from a filename and limit its length.
    
    Args:
        filename (str): The original filename
        
    Returns:
        str: A sanitized filename
    """
    # Remove illegal characters and limit filename length
    cleaned = re.sub(r'[\\/*?:"<>|]', "", filename)[:100]
    return cleaned or "untitled"

def show_banner() -> None:
    """Display the application banner."""
    print(Fore.CYAN + r"""
     ____  _             _          _   _             
    |  _ \| |_   _  __ _| |__      | | | |___  ___ _ __ 
    | | | | | | | |/ _` | '_ \  _  | | | / __|/ _ \ '__|
    | |_| | | |_| | (_| | | | || | | |_| \__ \  __/ |   
    |____/|_|\__,_|\__,_|_| |_(_)  \___/|___/\___|_|   
    """ + f"\n{' ' * 30}v{VERSION}")

def setup_logging() -> None:
    """Configure logging for the application."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = log_dir / f"{timestamp}_{LOG_FILE}"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    logging.info(f"Logging initialized. Log file: {log_file}")

def check_network_connection(timeout: float = 3.0) -> bool:
    """
    检查网络连接是否可用
    
    Args:
        timeout (float): 超时时间（秒）
        
    Returns:
        bool: 网络是否可用
    """
    try:
        # 尝试连接到常用网站
        requests.get("https://www.baidu.com", timeout=timeout)
        return True
    except requests.RequestException:
        try:
            # 备用检测
            requests.get("https://www.qq.com", timeout=timeout)
            return True
        except requests.RequestException:
            return False

class Config:
    """Configuration manager for the application."""
    
    def __init__(self, config_file: str = "config.json"):
        """
        Initialize the configuration manager.
        
        Args:
            config_file (str): Path to the configuration file
        """
        self.config_file = config_file
        self.config: Dict[str, Any] = self._load_config()
        self.debug = self.config.get("debug", False)

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file or create default if not exists.
        
        Returns:
            Dict[str, Any]: The configuration dictionary
        """
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        """
        Create and save default configuration.
        
        Returns:
            Dict[str, Any]: The default configuration dictionary
        """
        # Get user's home directory for more portable default path
        home_dir = str(Path.home())
        downloads_dir = os.path.join(home_dir, "Downloads")
        
        default_config = {
            "save_dir": downloads_dir,
            "headers": HEADERS,
            "debug": False,
            "default_file_type": DEFAULT_FILE_TYPE,
            "recent_downloads": [],
            "max_recent_items": 10
        }
        self.save_config(default_config)
        return default_config

    def save_config(self, config: Dict[str, Any]) -> None:
        """
        Save configuration to file.
        
        Args:
            config (Dict[str, Any]): The configuration to save
        """
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4)

    def toggle_debug(self) -> None:
        """Toggle debug mode and save the setting."""
        self.debug = not self.debug
        self.config["debug"] = self.debug
        self.save_config(self.config)
        print_status(f"调试模式 {'已启用' if self.debug else '已禁用'}", "warning")
        
    def add_recent_download(self, url: str, filename: str, path: str) -> None:
        """
        Add a download to the recent downloads list.
        
        Args:
            url (str): The download URL
            filename (str): The filename
            path (str): The save path
        """
        if "recent_downloads" not in self.config:
            self.config["recent_downloads"] = []
            
        # Add new download to the beginning of the list
        self.config["recent_downloads"].insert(0, {
            "url": url,
            "filename": filename,
            "path": path,
            "timestamp": datetime.now().isoformat()
        })
        
        # Limit the number of recent downloads
        max_items = self.config.get("max_recent_items", 10)
        if len(self.config["recent_downloads"]) > max_items:
            self.config["recent_downloads"] = self.config["recent_downloads"][:max_items]
            
        self.save_config(self.config)

class DownloaderGUI:
    """GUI for the file downloader application."""
    
    def __init__(self, root):
        """
        Initialize the GUI.
        
        Args:
            root: The tkinter root window
        """
        self.root = root
        self.root.title(f"文件下载器 v{VERSION}")
        self.root.geometry("650x500")
        self.root.minsize(600, 400)
        
        # Set application icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass  # Icon not available, use default
            
        self.config = Config()
        self.file_type = StringVar(value=self.config.config.get("default_file_type", DEFAULT_FILE_TYPE))
        
        # Initialize logging
        self.setup_logging()
        
        # Setup UI components
        self.setup_ui()
        
        # Show welcome message
        self.update_status(f"欢迎使用文件下载器 v{VERSION}", "info")
        
        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_logging(self) -> None:
        """Configure logging for the application."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = log_dir / f"{timestamp}_{LOG_FILE}"
        
        logging.basicConfig(
            level=logging.INFO if not self.config.debug else logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        logging.info(f"Application started. Version: {VERSION}")

    def setup_ui(self) -> None:
        """Set up the user interface components."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # URL input frame
        url_frame = ttk.LabelFrame(main_frame, text="下载信息", padding="5")
        url_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # URL input
        ttk.Label(url_frame, text="下载链接:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.url_entry = ttk.Entry(url_frame)
        self.url_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # Paste button
        ttk.Button(url_frame, text="粘贴", command=self.paste_url).grid(row=0, column=2, padx=5, pady=5)
        
        # Filename input
        ttk.Label(url_frame, text="文件名:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.name_entry = ttk.Entry(url_frame)
        self.name_entry.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # File type selection
        ttk.Label(url_frame, text="文件类型:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        file_type_combo = ttk.Combobox(url_frame, textvariable=self.file_type, values=SUPPORTED_FILE_TYPES, width=6)
        file_type_combo.grid(row=1, column=3, padx=5, pady=5)
        
        # Directory selection
        ttk.Label(url_frame, text="保存目录:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.dir_entry = ttk.Entry(url_frame)
        self.dir_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        self.dir_entry.insert(0, self.config.config["save_dir"])
        ttk.Button(url_frame, text="浏览", command=self.choose_directory).grid(row=2, column=2, padx=5, pady=5)
        
        # Configure grid column weights
        url_frame.columnconfigure(1, weight=1)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="下载进度", padding="5")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(progress_frame, mode='determinate', length=100)
        self.progress.pack(fill=tk.X, padx=5, pady=5)
        
        # Progress label
        self.progress_label = ttk.Label(progress_frame, text="0%")
        self.progress_label.pack(anchor=tk.E, padx=5)
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="5")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Status text with scrollbar
        status_scroll = ttk.Scrollbar(status_frame)
        status_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.status_text = tk.Text(status_frame, height=10, wrap=tk.WORD, yscrollcommand=status_scroll.set)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        status_scroll.config(command=self.status_text.yview)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Download button
        self.download_btn = ttk.Button(button_frame, text="开始下载", command=self.start_download)
        self.download_btn.pack(side=tk.RIGHT, padx=5)
        
        # Clear button
        ttk.Button(button_frame, text="清空", command=self.clear_fields).pack(side=tk.RIGHT, padx=5)
        
        # Recent downloads button
        ttk.Button(button_frame, text="最近下载", command=self.show_recent_downloads).pack(side=tk.LEFT, padx=5)
        
        # About button
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.LEFT, padx=5)

    def paste_url(self) -> None:
        """Paste clipboard content into URL entry."""
        try:
            clipboard = self.root.clipboard_get()
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, clipboard)
        except:
            self.update_status("无法获取剪贴板内容", "warning")

    def choose_directory(self) -> None:
        """Open directory selection dialog."""
        directory = filedialog.askdirectory(initialdir=self.dir_entry.get())
        if directory:
            self.dir_entry.delete(0, tk.END)
            self.dir_entry.insert(0, directory)
            self.config.config["save_dir"] = directory
            self.config.save_config(self.config.config)

    def update_status(self, message: str, level: str = "info") -> None:
        """
        Update status text and log the message.
        
        Args:
            message (str): The message to display
            level (str): Log level (info, warning, error, success)
        """
        # Map status levels to logging levels
        log_levels = {
            "info": logging.INFO,
            "warning": logging.WARNING,
            "error": logging.ERROR,
            "success": logging.INFO
        }
        
        # Add timestamp to message
        timestamp = datetime.now().strftime("%H:%M:%S")
        display_message = f"[{timestamp}] {message}"
        
        # Update UI
        self.status_text.insert(tk.END, f"{display_message}\n")
        self.status_text.see(tk.END)
        
        # Log message
        logging.log(log_levels.get(level, logging.INFO), message)

    def update_progress(self, value: float) -> None:
        """
        Update progress bar and label.
        
        Args:
            value (float): Progress percentage (0-100)
        """
        self.progress['value'] = value
        self.progress_label.config(text=f"{int(value)}%")
        self.root.update_idletasks()

    def clear_fields(self) -> None:
        """Clear input fields."""
        self.url_entry.delete(0, tk.END)
        self.name_entry.delete(0, tk.END)
        self.progress['value'] = 0
        self.progress_label.config(text="0%")

    def show_recent_downloads(self) -> None:
        """Show recent downloads in a new window."""
        recent_downloads = self.config.config.get("recent_downloads", [])
        
        if not recent_downloads:
            messagebox.showinfo("最近下载", "没有最近的下载记录")
            return
            
        # Create new window
        recent_window = tk.Toplevel(self.root)
        recent_window.title("最近下载")
        recent_window.geometry("600x400")
        recent_window.transient(self.root)
        recent_window.grab_set()
        
        # Create treeview
        columns = ("filename", "path", "timestamp")
        tree = ttk.Treeview(recent_window, columns=columns, show="headings")
        
        # Define headings
        tree.heading("filename", text="文件名")
        tree.heading("path", text="保存路径")
        tree.heading("timestamp", text="下载时间")
        
        # Define column widths
        tree.column("filename", width=150)
        tree.column("path", width=300)
        tree.column("timestamp", width=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(recent_window, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack widgets
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Add data
        for item in recent_downloads:
            # Format timestamp
            try:
                dt = datetime.fromisoformat(item.get("timestamp", ""))
                timestamp = dt.strftime("%Y-%m-%d %H:%M:%S")
            except:
                timestamp = "未知"
                
            tree.insert("", tk.END, values=(
                item.get("filename", "未知"),
                item.get("path", "未知"),
                timestamp
            ))
            
        # Add button to reuse selected download
        def reuse_selected():
            selected = tree.selection()
            if selected:
                item_id = selected[0]
                item_index = tree.index(item_id)
                if 0 <= item_index < len(recent_downloads):
                    download = recent_downloads[item_index]
                    self.url_entry.delete(0, tk.END)
                    self.url_entry.insert(0, download.get("url", ""))
                    self.name_entry.delete(0, tk.END)
                    self.name_entry.insert(0, download.get("filename", ""))
                    recent_window.destroy()
        
        ttk.Button(recent_window, text="使用选中的下载", command=reuse_selected).pack(pady=10)

    def show_about(self) -> None:
        """Show about dialog."""
        about_text = f"""文件下载器 v{VERSION}

一个简单的文件下载工具，支持各种文件类型的下载。

作者: hgxszhj
邮箱: <EMAIL>
日期: 2024-05-29
"""
        messagebox.showinfo("关于", about_text)

    def start_download(self) -> None:
        """Validate input and start download process."""
        # Validate URL
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入下载链接")
            return
        if not url.startswith(('http://', 'https://')):
            messagebox.showerror("错误", "链接格式不正确，必须以http://或https://开头")
            return

        # Validate filename
        raw_name = self.name_entry.get().strip()
        if not raw_name:
            messagebox.showerror("错误", "请输入文件名")
            return

        # Get file type
        file_type = self.file_type.get()
        
        # Sanitize filename and create path
        safe_name = sanitize_filename(raw_name)
        save_dir = Path(self.dir_entry.get())
        save_dir.mkdir(parents=True, exist_ok=True)
        file_path = save_dir / f"{safe_name}{file_type}"

        # Check if file exists
        if file_path.exists():
            if not messagebox.askyesno("确认", "文件已存在，是否覆盖？"):
                self.update_status("已取消下载", "warning")
                return

        # Disable download button and reset progress
        self.download_btn.state(['disabled'])
        self.progress['value'] = 0
        self.progress_label.config(text="0%")
        
        # Start download in a separate thread
        thread = threading.Thread(
            target=self.download_thread, 
            args=(url, file_path, safe_name)
        )
        thread.daemon = True
        thread.start()

    def download_thread(self, url: str, file_path: Path, filename: str) -> None:
        """
        Download thread to prevent UI freezing.
        
        Args:
            url (str): The URL to download from
            file_path (Path): The path to save the file to
            filename (str): The sanitized filename
        """
        try:
            self.update_status(f"开始下载: {filename}", "info")
            session = create_session()
            
            # Update UI to show download started
            self.root.after(0, lambda: self.update_status("正在下载文件...", "info"))
            
            # Make the request
            response = session.get(url, headers=HEADERS, timeout=(3.05, 30))
            response.raise_for_status()
            
            # Get the data
            try:
                data = base64.b64decode(response.json()['body'])
                total_size = len(data)
                
                # Write the file with progress updates
                with open(file_path, 'wb') as f:
                    chunk_size = max(1, total_size // 100)
                    for i in range(0, total_size, chunk_size):
                        end = min(i + chunk_size, total_size)
                        f.write(data[i:end])
                        progress = (end / total_size) * 100
                        self.root.after(0, lambda p=progress: self.update_progress(p))
                        time.sleep(0.01)  # Small delay to allow UI updates
                
                # Add to recent downloads
                self.config.add_recent_download(url, filename, str(file_path))
                
                # Show success message
                self.root.after(0, lambda: self.update_status(f"文件已成功保存至：{file_path}", "success"))
                self.root.after(0, lambda: messagebox.showinfo("成功", "下载完成！"))
                
            except KeyError:
                self.root.after(0, lambda: self.update_status("响应数据格式异常，缺少'body'字段", "error"))
                self.root.after(0, lambda: messagebox.showerror("错误", "响应数据格式异常，缺少'body'字段"))
                
        except Exception as e:
            error_msg = f"下载失败: {str(e)}"
            self.root.after(0, lambda: self.update_status(error_msg, "error"))
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            
        finally:
            # Re-enable download button
            self.root.after(0, self.finish_download)

    def finish_download(self) -> None:
        """Reset UI after download completes."""
        self.download_btn.state(['!disabled'])

    def on_closing(self) -> None:
        """Handle window close event."""
        # Save any pending configuration changes
        self.config.save_config(self.config.config)
        logging.info("Application closed")
        self.root.destroy()

def main():
    """Main entry point for the application."""
    # Setup logging
    setup_logging()
    
    # Show banner in console
    show_banner()
    
    # Create and run GUI
    root = tk.Tk()
    app = DownloaderGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
