'''
Author: hgxszhj <EMAIL>
Date: 2024-05-29 15:56:53
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2025-03-01 09:11:00
FilePath: /my_project/2024-5-20/download_myctu_v01.py
Description: File download utility with configurable settings and error handling
'''
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, StringVar
import requests
import json
import base64
from pathlib import Path
import re
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from colorama import Fore, init
from typing import Dict, Any, Optional, Tuple, List
import logging
import os
from dotenv import load_dotenv
import threading
import time
import sys
from datetime import datetime
import psutil  # 添加psutil库用于监控内存使用
import gc

# Initialize colorama
init(autoreset=True)

# Load environment variables
load_dotenv()

# Constants
DEBUG = False
VERSION = "0.2.0"
DEFAULT_FILE_TYPE = ".pdf"
SUPPORTED_FILE_TYPES = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar"]
LOG_FILE = "download_logs.log"

# Default headers for HTTP requests
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Cookie": os.getenv("COOKIE", ""),
    "Authorization": os.getenv("AUTH_TOKEN", "")
}

def create_session() -> requests.Session:
    """
    Create and configure a requests session with retry capabilities.
    
    Returns:
        requests.Session: Configured session object
    """
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504]
    )
    session.mount('https://', HTTPAdapter(max_retries=retries))
    session.mount('http://', HTTPAdapter(max_retries=retries))
    return session

def print_status(message: str, status: str = "info") -> None:
    """
    Print a status message with color coding.
    
    Args:
        message (str): The message to print
        status (str): Status type (info, success, error, warning)
    """
    colors = {
        "info": Fore.CYAN,
        "success": Fore.GREEN,
        "error": Fore.RED,
        "warning": Fore.YELLOW
    }
    print(f"{colors.get(status, Fore.WHITE)}{message}")

def download_file(url: str, file_path: str, headers: dict, progress_callback=None) -> bool:
    """
    Download a file from a URL and save it to the specified path.
    
    Args:
        url (str): The URL to download from
        file_path (str): The path to save the file to
        headers (dict): HTTP headers to use for the request
        progress_callback (callable, optional): Callback function for progress updates
        
    Returns:
        bool: True if download was successful, False otherwise
    """
    try:
        session = create_session()
        print_status("正在下载文件...", "info")
        
        # Stream the response to handle progress
        with session.get(url, headers=headers, timeout=(3.05, 30), stream=True) as response:
            response.raise_for_status()
            
            # 使用流式处理，避免一次性加载整个文件到内存
            response_json = response.json()
            if 'body' not in response_json:
                raise KeyError("响应数据格式异常，缺少'body'字段")
                
            # 获取base64编码的数据
            b64_data = response_json['body']
            # 估算解码后的大小（每4个base64字符大约解码为3个字节）
            estimated_size = len(b64_data) * 3 // 4
            
            # 分块处理base64数据
            chunk_size = 8192  # 8KB的块大小
            b64_chunks = [b64_data[i:i+chunk_size] for i in range(0, len(b64_data), chunk_size)]
            total_chunks = len(b64_chunks)
            
            with open(file_path, 'wb') as f:
                for i, b64_chunk in enumerate(b64_chunks):
                    # 解码当前块
                    try:
                        # 添加必要的填充
                        padding_needed = len(b64_chunk) % 4
                        if padding_needed:
                            b64_chunk += '=' * (4 - padding_needed)
                        
                        # 解码并写入文件
                        decoded_chunk = base64.b64decode(b64_chunk)
                        f.write(decoded_chunk)
                    except Exception as e:
                        print_status(f"解码块 {i+1}/{total_chunks} 时出错: {str(e)}", "error")
                        raise
                    
                    # 更新进度
                    if progress_callback:
                        progress = ((i + 1) / total_chunks) * 100
                        progress_callback(progress)
                        time.sleep(0.01)  # Small delay to allow UI updates
                
        print_status(f"文件已成功保存至：{file_path}", "success")
        return True
        
    except requests.exceptions.RequestException as e:
        print_status(f"网络请求失败: {str(e)}", "error")
    except KeyError as e:
        print_status(f"响应数据格式异常: {str(e)}", "error")
    except IOError as e:
        print_status(f"文件保存失败: {str(e)}", "error")
    except Exception as e:
        print_status(f"发生未知错误: {str(e)}", "error")
    return False

def sanitize_filename(filename: str) -> str:
    """
    Remove illegal characters from a filename and limit its length.
    
    Args:
        filename (str): The original filename
        
    Returns:
        str: A sanitized filename
    """
    # Remove illegal characters and limit filename length
    cleaned = re.sub(r'[\\/*?:"<>|]', "", filename)[:100]
    return cleaned or "untitled"

def show_banner() -> None:
    """Display the application banner."""
    print(Fore.CYAN + r"""
     ____  _             _          _   _             
    |  _ \| |_   _  __ _| |__      | | | |___  ___ _ __ 
    | | | | | | | |/ _` | '_ \  _  | | | / __|/ _ \ '__|
    | |_| | | |_| | (_| | | | || | | |_| \__ \  __/ |   
    |____/|_|\__,_|\__,_|_| |_(_)  \___/|___/\___|_|   
    """ + f"\n{' ' * 30}v{VERSION}")

def setup_logging() -> None:
    """Configure logging for the application."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = log_dir / f"{timestamp}_{LOG_FILE}"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    logging.info(f"Logging initialized. Log file: {log_file}")

def check_network_connection(timeout: float = 3.0) -> bool:
    """
    检查网络连接是否可用
    
    Args:
        timeout (float): 超时时间（秒）
        
    Returns:
        bool: 网络是否可用
    """
    try:
        # 尝试连接到常用网站
        requests.get("https://www.baidu.com", timeout=timeout)
        return True
    except requests.RequestException:
        try:
            # 备用检测
            requests.get("https://www.qq.com", timeout=timeout)
            return True
        except requests.RequestException:
            return False

def get_memory_usage() -> Tuple[float, float, float]:
    """
    获取当前进程的内存使用情况
    
    Returns:
        Tuple[float, float, float]: (当前使用内存MB, 总内存MB, 使用百分比)
    """
    # 获取当前进程信息
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    # 获取物理内存使用情况 (RSS - Resident Set Size)
    memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
    
    # 获取系统总内存
    system_memory = psutil.virtual_memory()
    total_memory_mb = system_memory.total / 1024 / 1024
    memory_percent = system_memory.percent
    
    return memory_mb, total_memory_mb, memory_percent

def optimize_memory() -> None:
    """
    优化内存使用，清理不再使用的对象
    """
    # 强制垃圾回收
    gc.collect()
    
    # 尝试释放未使用的内存回操作系统（仅在某些系统上有效）
    if hasattr(gc, 'garbage'):
        del gc.garbage[:]
    
    # 在Linux系统上，可以尝试调用malloc_trim
    if sys.platform.startswith('linux'):
        try:
            import ctypes
            libc = ctypes.CDLL('libc.so.6')
            libc.malloc_trim(0)
        except:
            pass

class Config:
    """Configuration manager for the application."""
    
    def __init__(self, config_file: str = "config.json"):
        """
        Initialize the configuration manager.
        
        Args:
            config_file (str): Path to the configuration file
        """
        self.config_file = config_file
        self.config: Dict[str, Any] = self._load_config()
        self.debug = self.config.get("debug", False)

    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file or create default if not exists.
        
        Returns:
            Dict[str, Any]: The configuration dictionary
        """
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        """
        Create and save default configuration.
        
        Returns:
            Dict[str, Any]: The default configuration dictionary
        """
        # Get user's home directory for more portable default path
        home_dir = str(Path.home())
        downloads_dir = os.path.join(home_dir, "Downloads")
        
        default_config = {
            "save_dir": downloads_dir,
            "headers": HEADERS,
            "debug": False,
            "default_file_type": DEFAULT_FILE_TYPE,
            "recent_downloads": [],
            "max_recent_items": 10
        }
        self.save_config(default_config)
        return default_config

    def save_config(self, config: Dict[str, Any]) -> None:
        """
        Save configuration to file.
        
        Args:
            config (Dict[str, Any]): The configuration to save
        """
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4)

    def toggle_debug(self) -> None:
        """Toggle debug mode and save the setting."""
        self.debug = not self.debug
        self.config["debug"] = self.debug
        self.save_config(self.config)
        print_status(f"调试模式 {'已启用' if self.debug else '已禁用'}", "warning")
        
    def add_recent_download(self, url: str, filename: str, path: str) -> None:
        """
        Add a download to the recent downloads list.
        
        Args:
            url (str): The download URL
            filename (str): The filename
            path (str): The save path
        """
        if "recent_downloads" not in self.config:
            self.config["recent_downloads"] = []
            
        # Add new download to the beginning of the list
        self.config["recent_downloads"].insert(0, {
            "url": url,
            "filename": filename,
            "path": path,
            "timestamp": datetime.now().isoformat()
        })
        
        # Limit the number of recent downloads
        max_items = self.config.get("max_recent_items", 10)
        if len(self.config["recent_downloads"]) > max_items:
            self.config["recent_downloads"] = self.config["recent_downloads"][:max_items]
            
        self.save_config(self.config)

class DownloaderGUI:
    """GUI for the file downloader application."""
    
    def __init__(self, root):
        """
        Initialize the GUI.
        
        Args:
            root: The tkinter root window
        """
        self.root = root
        self.root.title(f"文件下载器 v{VERSION}")
        self.root.geometry("650x500")
        self.root.minsize(600, 400)
        
        # 优化内存使用
        optimize_memory()
        
        # Set application icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass  # Icon not available, use default
            
        self.config = Config()
        self.file_type = StringVar(value=self.config.config.get("default_file_type", DEFAULT_FILE_TYPE))
        
        # 内存监控变量
        self.memory_var = StringVar(value="内存使用: 计算中...")
        self.memory_monitor_active = True
        
        # Initialize logging
        self.setup_logging()
        
        # Setup UI components
        self.setup_ui()
        
        # Show welcome message
        self.update_status(f"欢迎使用文件下载器 v{VERSION}", "info")
        
        # 启动内存监控
        self.start_memory_monitor()
        
        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_logging(self) -> None:
        """Configure logging for the application."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = log_dir / f"{timestamp}_{LOG_FILE}"
        
        logging.basicConfig(
            level=logging.INFO if not self.config.debug else logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        logging.info(f"Application started. Version: {VERSION}")

    def setup_ui(self) -> None:
        """Set up the user interface components."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # URL input frame
        url_frame = ttk.LabelFrame(main_frame, text="下载信息", padding="5")
        url_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # URL input
        ttk.Label(url_frame, text="下载链接:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.url_entry = ttk.Entry(url_frame)
        self.url_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # Paste button
        ttk.Button(url_frame, text="粘贴", command=self.paste_url).grid(row=0, column=2, padx=5, pady=5)
        
        # Filename input
        ttk.Label(url_frame, text="文件名:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.name_entry = ttk.Entry(url_frame)
        self.name_entry.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        
        # File type selection
        ttk.Label(url_frame, text="文件类型:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        file_type_combo = ttk.Combobox(url_frame, textvariable=self.file_type, values=SUPPORTED_FILE_TYPES, width=6)
        file_type_combo.grid(row=1, column=3, padx=5, pady=5)
        
        # Directory selection
        ttk.Label(url_frame, text="保存目录:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.dir_entry = ttk.Entry(url_frame)
        self.dir_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=5)
        self.dir_entry.insert(0, self.config.config["save_dir"])
        ttk.Button(url_frame, text="浏览", command=self.choose_directory).grid(row=2, column=2, padx=5, pady=5)
        
        # Configure grid column weights
        url_frame.columnconfigure(1, weight=1)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="下载进度", padding="5")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Progress bar
        self.progress = ttk.Progressbar(progress_frame, mode='determinate', length=100)
        self.progress.pack(fill=tk.X, padx=5, pady=5)
        
        # Progress label
        self.progress_label = ttk.Label(progress_frame, text="0%")
        self.progress_label.pack(anchor=tk.E, padx=5)
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="5")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Status text with scrollbar
        status_scroll = ttk.Scrollbar(status_frame)
        status_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.status_text = tk.Text(status_frame, height=10, wrap=tk.WORD, yscrollcommand=status_scroll.set)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        status_scroll.config(command=self.status_text.yview)
        
        # 内存使用信息标签
        memory_frame = ttk.Frame(main_frame)
        memory_frame.pack(fill=tk.X, padx=5, pady=2)
        
        self.memory_label = ttk.Label(memory_frame, textvariable=self.memory_var)
        self.memory_label.pack(side=tk.LEFT, padx=5)
        
        # 添加刷新内存信息按钮
        ttk.Button(memory_frame, text="刷新", width=6, 
                  command=self.update_memory_info).pack(side=tk.LEFT, padx=5)
        
        # 添加内存清理按钮
        ttk.Button(memory_frame, text="清理内存", width=8,
                  command=self.clean_memory).pack(side=tk.LEFT, padx=5)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Download button
        self.download_btn = ttk.Button(button_frame, text="开始下载", command=self.start_download)
        self.download_btn.pack(side=tk.RIGHT, padx=5)
        
        # Clear button
        ttk.Button(button_frame, text="清空", command=self.clear_fields).pack(side=tk.RIGHT, padx=5)
        
        # Recent downloads button
        ttk.Button(button_frame, text="最近下载", command=self.show_recent_downloads).pack(side=tk.LEFT, padx=5)
        
        # About button
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.LEFT, padx=5)

    def paste_url(self) -> None:
        """Paste clipboard content into URL entry."""
        try:
            clipboard = self.root.clipboard_get()
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, clipboard)
        except:
            self.update_status("无法获取剪贴板内容", "warning")

    def choose_directory(self) -> None:
        """Open directory selection dialog."""
        directory = filedialog.askdirectory(initialdir=self.dir_entry.get())
        if directory:
            self.dir_entry.delete(0, tk.END)
            self.dir_entry.insert(0, directory)
            self.config.config["save_dir"] = directory
            self.config.save_config(self.config.config)

    def update_status(self, message: str, level: str = "info") -> None:
        """
        Update status text and log the message.
        
        Args:
            message (str): The message to display
            level (str): Log level (info, warning, error, success)
        """
        # Map status levels to logging levels
        log_levels = {
            "info": logging.INFO,
            "warning": logging.WARNING,
            "error": logging.ERROR,
            "success": logging.INFO
        }
        
        # Add timestamp to message
        timestamp = datetime.now().strftime("%H:%M:%S")
        display_message = f"[{timestamp}] {message}"
        
        # Update UI
        self.status_text.insert(tk.END, f"{display_message}\n")
        self.status_text.see(tk.END)
        
        # Log message
        logging.log(log_levels.get(level, logging.INFO), message)

    def update_progress(self, value: float) -> None:
        """
        Update progress bar and label.
        
        Args:
            value (float): Progress percentage (0-100)
        """
        self.progress['value'] = value
        self.progress_label.config(text=f"{int(value)}%")
        self.root.update_idletasks()

    def clear_fields(self) -> None:
        """Clear input fields."""
        self.url_entry.delete(0, tk.END)
        self.name_entry.delete(0, tk.END)
        self.progress['value'] = 0
        self.progress_label.config(text="0%")

    def show_recent_downloads(self) -> None:
        """Show recent downloads in a new window."""
        recent_downloads = self.config.config.get("recent_downloads", [])
        
        if not recent_downloads:
            messagebox.showinfo("最近下载", "没有最近的下载记录")
            return
            
        # Create new window
        recent_window = tk.Toplevel(self.root)
        recent_window.title("最近下载")
        recent_window.geometry("600x400")
        recent_window.transient(self.root)
        recent_window.grab_set()
        
        # Create treeview
        columns = ("filename", "path", "timestamp")
        tree = ttk.Treeview(recent_window, columns=columns, show="headings")
        
        # Define headings
        tree.heading("filename", text="文件名")
        tree.heading("path", text="保存路径")
        tree.heading("timestamp", text="下载时间")
        
        # Define column widths
        tree.column("filename", width=150)
        tree.column("path", width=300)
        tree.column("timestamp", width=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(recent_window, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack widgets
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Populate treeview
        self.populate_treeview(tree, recent_downloads)

    def populate_treeview(self, tree, items, batch_size=50):
        """
        使用生成器分批填充TreeView
        
        Args:
            tree: TreeView控件
            items: 数据项列表
            batch_size: 每批处理的项目数
        """
        # 清空现有项目
        for item in tree.get_children():
            tree.delete(item)
        
        # 使用生成器分批添加
        def add_items_generator():
            for i in range(0, len(items), batch_size):
                batch = items[i:i+batch_size]
                for item in batch:
                    # 格式化时间戳
                    try:
                        dt = datetime.fromisoformat(item.get("timestamp", ""))
                        timestamp = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        timestamp = "未知"
                        
                    tree.insert("", tk.END, values=(
                        item.get("filename", "未知"),
                        item.get("path", "未知"),
                        timestamp
                    ))
                # 让出控制权，避免UI冻结
                yield
        
        # 创建生成器
        gen = add_items_generator()
        
        # 使用after方法分批处理
        def process_batch():
            try:
                next(gen)
                tree.after(10, process_batch)  # 10ms后处理下一批
            except StopIteration:
                pass  # 处理完成
        
        # 开始处理
        process_batch()

    def show_about(self) -> None:
        """Show about dialog."""
        about_text = f"""文件下载器 v{VERSION}

一个简单的文件下载工具，支持各种文件类型的下载。

作者: hgxszhj
邮箱: <EMAIL>
日期: 2024-05-29
"""
        messagebox.showinfo("关于", about_text)

    def start_download(self) -> None:
        """Validate input and start download process."""
        # Validate URL
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入下载链接")
            return
        if not url.startswith(('http://', 'https://')):
            messagebox.showerror("错误", "链接格式不正确，必须以http://或https://开头")
            return

        # Validate filename
        raw_name = self.name_entry.get().strip()
        if not raw_name:
            messagebox.showerror("错误", "请输入文件名")
            return

        # Get file type
        file_type = self.file_type.get()
        
        # Sanitize filename and create path
        safe_name = sanitize_filename(raw_name)
        save_dir = Path(self.dir_entry.get())
        save_dir.mkdir(parents=True, exist_ok=True)
        file_path = save_dir / f"{safe_name}{file_type}"

        # Check if file exists
        if file_path.exists():
            if not messagebox.askyesno("确认", "文件已存在，是否覆盖？"):
                self.update_status("已取消下载", "warning")
                return

        # Disable download button and reset progress
        self.download_btn.state(['disabled'])
        self.progress['value'] = 0
        self.progress_label.config(text="0%")
        
        # Start download in a separate thread
        thread = threading.Thread(
            target=self.download_thread, 
            args=(url, file_path, safe_name)
        )
        thread.daemon = True
        thread.start()

    def download_thread(self, url: str, file_path: Path, filename: str) -> None:
        """
        Download thread to prevent UI freezing.
        
        Args:
            url (str): The URL to download from
            file_path (Path): The path to save the file to
            filename (str): The sanitized filename
        """
        try:
            self.update_status(f"开始下载: {filename}", "info")
            
            # 记录下载前的内存使用
            before_mb, total_mb, before_percent = get_memory_usage()
            self.update_status(f"下载前内存使用: {before_mb:.1f}MB ({before_percent:.1f}%)", "info")
            
            session = create_session()
            
            # Update UI to show download started
            self.root.after(0, lambda: self.update_status("正在下载文件...", "info"))
            
            # 使用流式处理下载文件
            with session.get(url, headers=HEADERS, timeout=(3.05, 30), stream=True) as response:
                response.raise_for_status()
                
                # 解析JSON响应
                response_json = response.json()
                if 'body' not in response_json:
                    raise KeyError("响应数据格式异常，缺少'body'字段")
                    
                # 获取base64编码的数据
                b64_data = response_json['body']
                # 估算解码后的大小（每4个base64字符大约解码为3个字节）
                estimated_size = len(b64_data) * 3 // 4
                
                # 更新UI显示文件大小
                self.root.after(0, lambda: self.update_status(
                    f"文件大小(估计): {estimated_size/1024/1024:.1f}MB", "info"))
                
                # 分块处理base64数据
                chunk_size = 8192  # 8KB的块大小
                total_chunks = (len(b64_data) + chunk_size - 1) // chunk_size  # 向上取整
                
                # 记录处理开始时的内存
                mid_mb, _, mid_percent = get_memory_usage()
                self.root.after(0, lambda: self.update_status(
                    f"开始处理前内存: {mid_mb:.1f}MB ({mid_percent:.1f}%)", "info"))
                
                with open(file_path, 'wb') as f:
                    processed_size = 0
                    
                    # 分块处理
                    for i in range(0, len(b64_data), chunk_size):
                        # 获取当前块
                        end = min(i + chunk_size, len(b64_data))
                        b64_chunk = b64_data[i:end]
                        
                        # 添加必要的填充
                        padding_needed = len(b64_chunk) % 4
                        if padding_needed:
                            b64_chunk += '=' * (4 - padding_needed)
                        
                        # 解码并写入文件
                        try:
                            decoded_chunk = base64.b64decode(b64_chunk)
                            f.write(decoded_chunk)
                            processed_size += len(decoded_chunk)
                        except Exception as e:
                            self.root.after(0, lambda: self.update_status(
                                f"解码错误: {str(e)}", "error"))
                            raise
                        
                        # 更新进度
                        chunk_index = i // chunk_size
                        progress = ((chunk_index + 1) / total_chunks) * 100
                        self.root.after(0, lambda p=progress: self.update_progress(p))
                        
                        # 每处理10个块检查一次内存使用情况
                        if chunk_index % 10 == 0:
                            # 强制垃圾回收
                            import gc
                            gc.collect()
                        
                        # 让出CPU时间，避免UI冻结
                        time.sleep(0.01)
                
                # 记录下载后的内存使用
                after_mb, _, after_percent = get_memory_usage()
                self.root.after(0, lambda: self.update_status(
                    f"下载完成，实际文件大小: {processed_size/1024/1024:.1f}MB", "info"))
                self.root.after(0, lambda: self.update_status(
                    f"下载完成后内存使用: {after_mb:.1f}MB ({after_percent:.1f}%)", "info"))
                
                # 强制垃圾回收
                import gc
                gc.collect()
                
                # 再次检查内存使用
                final_mb, _, final_percent = get_memory_usage()
                self.root.after(0, lambda: self.update_status(
                    f"垃圾回收后内存使用: {final_mb:.1f}MB ({final_percent:.1f}%)", "info"))
                
                # Add to recent downloads
                self.config.add_recent_download(url, filename, str(file_path))
                
                # Show success message
                self.root.after(0, lambda: self.update_status(f"文件已成功保存至：{file_path}", "success"))
                self.root.after(0, lambda: messagebox.showinfo("成功", "下载完成！"))
                
                # 更新内存信息
                self.root.after(100, self.update_memory_info)
                
        except KeyError as e:
            self.root.after(0, lambda: self.update_status(f"响应数据格式异常: {str(e)}", "error"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"响应数据格式异常: {str(e)}"))
        except Exception as e:
            error_msg = f"下载失败: {str(e)}"
            self.root.after(0, lambda: self.update_status(error_msg, "error"))
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        finally:
            # 再次强制垃圾回收
            import gc
            gc.collect()
            
            # Re-enable download button
            self.root.after(0, self.finish_download)

    def finish_download(self) -> None:
        """Reset UI after download completes."""
        self.download_btn.state(['!disabled'])
        
        # 优化内存使用
        optimize_memory()

    def on_closing(self) -> None:
        """Handle window close event."""
        # 停止内存监控
        self.memory_monitor_active = False
        
        # 优化内存使用
        optimize_memory()
        
        # Save any pending configuration changes
        self.config.save_config(self.config.config)
        logging.info("Application closed")
        self.root.destroy()

    def start_memory_monitor(self) -> None:
        """启动内存监控线程"""
        if not self.memory_monitor_active:
            return
            
        # 更新内存信息
        self.update_memory_info()
        
        # 每10秒更新一次，减少资源消耗
        self.root.after(10000, self.start_memory_monitor)
    
    def update_memory_info(self) -> None:
        """更新内存使用信息"""
        try:
            # 获取内存信息
            memory_mb, total_mb, percent = get_memory_usage()
            
            # 更新显示
            self.memory_var.set(f"内存使用: {memory_mb:.1f}MB / {total_mb:.0f}MB ({percent:.1f}%)")
            
            # 根据内存使用率设置颜色
            if percent > 90:
                self.memory_label.config(foreground="red")
                self.update_status("警告: 系统内存使用率超过90%，建议关闭其他应用", "warning")
            elif percent > 80:
                self.memory_label.config(foreground="orange")
                if not hasattr(self, '_last_warning_time') or time.time() - self._last_warning_time > 300:
                    self.update_status("提示: 系统内存使用率较高", "warning")
                    self._last_warning_time = time.time()
            elif percent > 70:
                self.memory_label.config(foreground="blue")
            else:
                self.memory_label.config(foreground="")
                
        except Exception as e:
            self.memory_var.set(f"内存信息获取失败: {str(e)}")
            logging.error(f"获取内存信息失败: {str(e)}")

    def clean_memory(self) -> None:
        """手动清理内存"""
        # 记录清理前的内存使用
        before_mb, _, before_percent = get_memory_usage()
        self.update_status(f"开始清理内存，当前使用: {before_mb:.1f}MB ({before_percent:.1f}%)", "info")
        
        # 执行内存优化
        optimize_memory()
        
        # 记录清理后的内存使用
        after_mb, _, after_percent = get_memory_usage()
        self.update_status(f"内存清理完成，当前使用: {after_mb:.1f}MB ({after_percent:.1f}%)", "info")
        self.update_status(f"释放内存: {before_mb - after_mb:.1f}MB", "success")
        
        # 更新内存信息显示
        self.update_memory_info()

def main():
    """Main entry point for the application."""
    # Setup logging
    setup_logging()
    
    # Show banner in console
    show_banner()
    
    # Create and run GUI
    root = tk.Tk()
    app = DownloaderGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
