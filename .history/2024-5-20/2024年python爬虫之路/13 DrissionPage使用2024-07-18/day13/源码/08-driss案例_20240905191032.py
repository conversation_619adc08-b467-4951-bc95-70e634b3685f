'''
Author: hgxszhj <EMAIL>
Date: 2024-07-20 07:36:30
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2024-09-05 19:10:32
FilePath: /my_project/2024-5-20/2024年python爬虫之路/13 DrissionPage使用2024-07-18/day13/源码/08-driss案例.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE%E5%8E%9F%E5%9B%BE
'''
from DrissionPage import ChromiumPage
import pymongo
from DrissionPage.common import By
import hashlib

class DouDing():
    def __init__(self, start_page=81, end_page=101):
        self.url = 'https://kaoyan.docin.com/pdfreader/web/#/docin/documents?type=1&keyword=%E5%A4%8C%E8%AF%95%E4%BB%BF%E7%9C%9F%E6%A8%A1%E6%8B%9F'
        self.page = ChromiumPage()
        self.page.listen.start('api/web/document/list')
        self.cli = pymongo.MongoClient(host='127.0.0.1')
        self.cll = self.cli['spiders16']['douding24']
        self.start_page = start_page  # 添加开始页
        self.end_page = end_page      # 添加结束页

    def get_data(self):
        try:
            self.page.get(self.url)
            for index, i in enumerate(self.page.listen.steps(count=self.end_page - self.start_page + 1), start=1):
                if index >= self.start_page:  # 使用索引来判断当前步骤
                    self.parse_data(i.response.body)
                    self.page.ele((By.CLASS_NAME, 'btn-next')).click()
        except Exception as e:
            print(f"Error in get_data: {e}")

    def parse_data(self, data):
        for i in data['Data']['DocumentInfos']:
            item = {}
            item['DocumentGuid'] = i['DocumentGuid']
            item['DocumentName'] = i['DocumentName']
            item['DocumentPrice'] = i['DocumentPrice']
            self.save_Data(item)

    def save_data(self, item):
        try:
            # 计算文档内容的哈希值
            item_hash = hashlib.md5(str(item).encode('utf-8')).hexdigest()
            
            # 检查哈希值是否已存在
            if not self.cll.find_one({"item_hash": item_hash}):
                print(item)
                item['item_hash'] = item_hash  # 将哈希值添加到文档中
                self.cll.insert_one(item)
            else:
                print(f"Document with hash {item_hash} already exists.")
        except Exception as e:
            print(f"Error saving data: {e}")

    def main(self):
        self.get_data()

if __name__ == '__main__':
    dd = DouDing()
    dd.main()









