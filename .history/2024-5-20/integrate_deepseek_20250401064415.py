"""
DeepSeek集成示例 - 展示如何将DeepSeek助手集成到下载器应用程序中
现代UI设计版本
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import threading
import logging
import time
from PIL import Image, ImageTk  # 需要安装: pip install pillow

# 导入DeepSeek助手
from deepseek_integration import DeepSeekAssistant

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('deepseek_integration.log')
    ]
)

# 颜色定义
COLORS = {
    "primary": "#1E88E5",       # 主色调 - 现代蓝
    "primary_dark": "#1565C0",  # 深主色
    "primary_light": "#64B5F6", # 浅主色
    "accent": "#00C853",        # 强调色 - 绿色
    "accent_dark": "#009624",   # 深强调色
    "accent_light": "#5EFC82",  # 浅强调色
    "dark_bg": "#121212",       # 深色背景
    "card_bg": "#1E1E1E",       # 卡片背景
    "light_text": "#FFFFFF",    # 浅色文字
    "dark_text": "#212121",     # 深色文字
    "grey_text": "#9E9E9E",     # 灰色文字
    "input_bg": "#FFFFFF",      # 输入框背景
    "border": "#424242",        # 边框颜色
    "error": "#F44336",         # 错误颜色
    "success": "#4CAF50",       # 成功颜色
}

class ModernButton(tk.Canvas):
    """现代风格按钮，支持渐变、悬停效果和波纹动画"""
    
    def __init__(self, parent, text, command, width=120, height=36, 
                 bg_color=COLORS["primary"], hover_color=COLORS["primary_dark"], 
                 fg_color=COLORS["light_text"], corner_radius=18, **kwargs):
        super().__init__(parent, width=width, height=height, 
                         bg=COLORS["dark_bg"], highlightthickness=0, **kwargs)
        
        self.bg_color = bg_color
        self.hover_color = hover_color
        self.fg_color = fg_color
        self.corner_radius = corner_radius
        self.width = width
        self.height = height
        self.command = command
        self.ripple_id = None
        self.ripples = []
        
        # 创建按钮
        self.create_rounded_rect()
        self.create_text_on_button(text)
        
        # 绑定事件
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
        self.bind("<Button-1>", self.on_click)
        self.bind("<ButtonRelease-1>", self.on_release)
    
    def create_rounded_rect(self, color=None):
        """创建圆角矩形"""
        if color is None:
            color = self.bg_color
            
        radius = self.corner_radius
        width, height = self.width, self.height
        
        # 清除画布
        self.delete("rounded_rect")
        
        # 创建圆角矩形
        self.create_oval(0, 0, radius*2, radius*2, 
                      fill=color, outline="", tags="rounded_rect")
        self.create_oval(width-radius*2, 0, width, radius*2, 
                      fill=color, outline="", tags="rounded_rect")
        self.create_oval(0, height-radius*2, radius*2, height, 
                      fill=color, outline="", tags="rounded_rect")
        self.create_oval(width-radius*2, height-radius*2, width, height, 
                      fill=color, outline="", tags="rounded_rect")
        
        self.create_rectangle(radius, 0, width-radius, height, 
                           fill=color, outline="", tags="rounded_rect")
        self.create_rectangle(0, radius, width, height-radius, 
                           fill=color, outline="", tags="rounded_rect")
    
    def create_text_on_button(self, text):
        """在按钮上创建文本"""
        self.delete("button_text")
        self.create_text(self.width/2, self.height/2, text=text, 
                      fill=self.fg_color, font=("Segoe UI", 10, "bold"), 
                      tags="button_text")
    
    def on_enter(self, event):
        """鼠标进入事件"""
        self.create_rounded_rect(self.hover_color)
        self.config(cursor="hand2")
    
    def on_leave(self, event):
        """鼠标离开事件"""
        self.create_rounded_rect(self.bg_color)
    
    def on_click(self, event):
        """鼠标点击事件"""
        self.start_ripple_animation(event.x, event.y)
    
    def on_release(self, event):
        """鼠标释放事件"""
        if 0 <= event.x <= self.width and 0 <= event.y <= self.height:
            if self.command:
                self.command()
    
    def start_ripple_animation(self, x, y):
        """开始波纹动画"""
        radius = 5
        max_radius = self.width
        ripple = self.create_oval(x-radius, y-radius, x+radius, y+radius, 
                               fill='#ffffff30', outline="", tags="ripple")
        self.ripples.append((ripple, x, y, radius, max_radius))
        self.animate_ripples()
    
    def animate_ripples(self):
        """动画波纹效果"""
        if not self.ripples:
            return
            
        new_ripples = []
        for ripple, x, y, radius, max_radius in self.ripples:
            radius += 5
            self.coords(ripple, x-radius, y-radius, x+radius, y+radius)
            opacity = int(100 - (radius / max_radius) * 100)
            if opacity > 0:
                self.itemconfig(ripple, fill=f'#ffffff{opacity:02x}')
                new_ripples.append((ripple, x, y, radius, max_radius))
            else:
                self.delete(ripple)
        
        self.ripples = new_ripples
        if self.ripples:
            self.after(20, self.animate_ripples)

class ModernFrame(tk.Frame):
    """现代风格框架，有渐变背景和阴影效果"""
    
    def __init__(self, parent, title=None, **kwargs):
        bg = kwargs.pop('bg', COLORS["dark_bg"])
        highlightbackground = kwargs.pop('highlightbackground', COLORS["border"])
        highlightthickness = kwargs.pop('highlightthickness', 1)
        
        super().__init__(parent, bg=bg, highlightbackground=highlightbackground,
                        highlightthickness=highlightthickness, **kwargs)
        
        if title:
            title_frame = tk.Frame(self, bg=COLORS["primary"], padx=10, pady=4)
            title_frame.pack(side=tk.TOP, fill=tk.X)
            
            title_label = tk.Label(title_frame, text=title, bg=COLORS["primary"],
                                fg=COLORS["light_text"], font=("Segoe UI", 11, "bold"))
            title_label.pack(side=tk.LEFT, anchor=tk.W)
        
        # 内容框架
        self.content_frame = tk.Frame(self, bg=bg, padx=15, pady=15)
        self.content_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

class ModernProgressBar(tk.Canvas):
    """现代风格进度条，支持动画和渐变效果"""
    
    def __init__(self, parent, width=400, height=8, **kwargs):
        super().__init__(parent, width=width, height=height, 
                         bg=COLORS["card_bg"], highlightthickness=0, **kwargs)
        
        self.width = width
        self.height = height
        self.progress = 0  # 0 to 100
        self.animated_progress = 0
        self.animation_id = None
        
        # 渐变色列表
        self.gradient_colors = self._create_gradient(
            COLORS["primary_light"], COLORS["primary"], 10)
        
        self.draw_progress()
    
    def _create_gradient(self, color1, color2, steps):
        """创建颜色渐变列表"""
        r1, g1, b1 = int(color1[1:3], 16), int(color1[3:5], 16), int(color1[5:7], 16)
        r2, g2, b2 = int(color2[1:3], 16), int(color2[3:5], 16), int(color2[5:7], 16)
        
        gradient = []
        for i in range(steps):
            t = i / (steps-1)
            r = int(r1 * (1-t) + r2 * t)
            g = int(g1 * (1-t) + g2 * t)
            b = int(b1 * (1-t) + b2 * t)
            gradient.append(f'#{r:02x}{g:02x}{b:02x}')
        
        return gradient
    
    def set_progress(self, progress):
        """设置进度值 (0-100)"""
        self.progress = max(0, min(100, progress))
        self.start_animation()
    
    def start_animation(self):
        """启动动画效果"""
        if self.animation_id:
            self.after_cancel(self.animation_id)
        
        # 动画更新进度条
        self._animate_to_target()
    
    def _animate_to_target(self):
        """动画更新到目标值"""
        if abs(self.animated_progress - self.progress) < 0.5:
            self.animated_progress = self.progress
            self.draw_progress()
            self.animation_id = None
        else:
            diff = (self.progress - self.animated_progress) / 10
            self.animated_progress += diff
            self.draw_progress()
            self.animation_id = self.after(16, self._animate_to_target)
    
    def draw_progress(self):
        """绘制进度条"""
        self.delete("progress")
        
        # 背景
        radius = self.height // 2
        self.create_rectangle(radius, 0, self.width - radius, self.height,
                           fill=COLORS["card_bg"], outline="", tags="progress")
        self.create_oval(0, 0, self.height, self.height,
                      fill=COLORS["card_bg"], outline="", tags="progress")
        self.create_oval(self.width - self.height, 0, self.width, self.height,
                      fill=COLORS["card_bg"], outline="", tags="progress")
        
        # 进度
        progress_width = int(self.width * (self.animated_progress / 100))
        if progress_width > self.height:
            # 渐变填充
            segment_width = progress_width / len(self.gradient_colors)
            for i, color in enumerate(self.gradient_colors):
                start_x = i * segment_width
                end_x = start_x + segment_width
                
                if start_x < radius:
                    # 左侧圆角
                    self.create_oval(start_x*2 - self.height, 0, start_x*2, self.height,
                                  fill=color, outline="", tags="progress")
                
                if end_x > self.width - radius:
                    # 右侧圆角
                    max_x = min(end_x, self.width)
                    self.create_oval(max_x*2 - self.height, 0, max_x*2, self.height,
                                  fill=color, outline="", tags="progress")
                
                # 矩形部分
                rect_start = max(start_x, radius)
                rect_end = min(end_x, self.width - radius)
                if rect_start < rect_end:
                    self.create_rectangle(rect_start, 0, rect_end, self.height,
                                      fill=color, outline="", tags="progress")
        elif progress_width > 0:
            # 小于高度时只显示一个圆形
            self.create_oval(0, 0, self.height, self.height,
                         fill=COLORS["primary"], outline="", tags="progress")

class LabelFrame(tk.Frame):
    """自定义标签框架，替代ttk.LabelFrame"""
    
    def __init__(self, parent, text, **kwargs):
        bg_color = kwargs.pop('bg', '#333333')
        fg_color = kwargs.pop('fg', '#00AA55')  # 改为深绿色以保持一致性
        
        kwargs['bg'] = bg_color
        super().__init__(parent, **kwargs)
        
        # 添加标签
        self.label = tk.Label(self, text=text, bg=bg_color, fg=fg_color, font=('Arial', 11, 'bold'))
        self.label.pack(anchor='w', padx=5, pady=(0, 5))
        
        # 内部框架
        self.inner_frame = tk.Frame(self, bg=bg_color)
        self.inner_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

class DeepSeekIntegrationDemo:
    """DeepSeek集成演示应用 - 现代UI设计版本"""
    
    def __init__(self, root):
        """初始化应用"""
        self.root = root
        self.root.title("DeepSeek AI助手")
        self.root.geometry("900x680")
        self.root.minsize(800, 600)
        
        # 初始化DeepSeek助手
        self.assistant = DeepSeekAssistant()
        
        # 加载图标和资源
        self.load_resources()
        
        # 设置主题
        self.setup_theme()
        
        # 设置UI
        self.setup_ui()
        
        # 显示加载动画
        self.show_loading_animation()
    
    def load_resources(self):
        """加载图标和资源"""
        # 此处可以加载自定义图标，这里使用简单的圆形代替
        self.icons = {
            "api": self._create_circle_icon(COLORS["primary"], "API"),
            "url": self._create_circle_icon(COLORS["primary"], "URL"),
            "file": self._create_circle_icon(COLORS["primary"], "文件"),
            "result": self._create_circle_icon(COLORS["accent"], "结果"),
            "settings": self._create_circle_icon(COLORS["grey_text"], "设置")
        }
    
    def _create_circle_icon(self, color, text=None, size=24):
        """创建简单的圆形图标"""
        img = Image.new("RGBA", (size, size), (0, 0, 0, 0))
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        
        # 绘制圆形
        draw.ellipse((0, 0, size, size), fill=color)
        
        # 添加文字
        if text and len(text) > 0:
            try:
                font = ImageFont.truetype("arial.ttf", size//2)
            except IOError:
                font = ImageFont.load_default()
            
            # 使用新版API替换过时的textsize
            text_char = text[0]
            # 获取文本边界框，计算中心位置
            left, top, right, bottom = draw.textbbox((0, 0), text_char, font=font)
            text_width = right - left
            text_height = bottom - top
            
            # 计算文本绘制位置使其居中
            position = ((size - text_width) // 2 - left, (size - text_height) // 2 - top)
            draw.text(position, text_char, fill=COLORS["light_text"], font=font)
        
        return ImageTk.PhotoImage(img)
    
    def setup_theme(self):
        """设置应用主题"""
        self.root.configure(bg=COLORS["dark_bg"])
        
        # 加载自定义字体
        self.fonts = {
            "heading": ("Segoe UI", 16, "bold"),
            "subheading": ("Segoe UI", 12, "bold"),
            "body": ("Segoe UI", 10),
            "small": ("Segoe UI", 9),
            "button": ("Segoe UI", 10, "bold")
        }
        
        # 创建自定义样式
        style = ttk.Style()
        
        # 设置ttk主题
        try:
            style.theme_use("clam")  # 使用适合自定义的主题
        except:
            pass  # 如果主题不可用，使用默认主题
        
        # 自定义Treeview
        style.configure("Treeview", 
                      background=COLORS["card_bg"], 
                      foreground=COLORS["light_text"],
                      fieldbackground=COLORS["card_bg"],
                      borderwidth=0)
        
        style.map("Treeview", 
                 background=[('selected', COLORS["primary"])],
                 foreground=[('selected', COLORS["light_text"])])
        
        # 自定义Scrollbar
        style.configure("TScrollbar", 
                      background=COLORS["card_bg"], 
                      bordercolor=COLORS["dark_bg"],
                      arrowcolor=COLORS["light_text"],
                      troughcolor=COLORS["dark_bg"])
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        self.main_container = tk.Frame(self.root, bg=COLORS["dark_bg"])
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 顶部标题栏
        self.create_header()
        
        # 左侧面板
        self.create_sidebar()
        
        # 右侧主内容区
        self.create_main_content()
        
        # 底部状态栏
        self.create_statusbar()
        
        # 检查API密钥状态
        self.check_api_key()
    
    def create_header(self):
        """创建顶部标题栏"""
        header = tk.Frame(self.main_container, bg=COLORS["primary"], height=60)
        header.pack(side=tk.TOP, fill=tk.X, pady=(0, 15))
        
        # 应用标题
        title_label = tk.Label(header, text="DeepSeek AI助手", 
                             bg=COLORS["primary"], fg=COLORS["light_text"],
                             font=self.fonts["heading"], padx=15)
        title_label.pack(side=tk.LEFT, pady=10)
        
        # 版本标签
        version_label = tk.Label(header, text="V1.0", 
                               bg=COLORS["primary"], fg=COLORS["primary_light"],
                               font=self.fonts["small"])
        version_label.pack(side=tk.LEFT, padx=5)
        
        # 右侧工具栏
        tools_frame = tk.Frame(header, bg=COLORS["primary"])
        tools_frame.pack(side=tk.RIGHT, padx=10)
        
        # 添加设置按钮
        settings_btn = tk.Label(tools_frame, image=self.icons["settings"],
                              bg=COLORS["primary"], cursor="hand2")
        settings_btn.pack(side=tk.RIGHT, padx=5)
        settings_btn.bind("<Button-1>", lambda e: self.show_settings())
    
    def create_sidebar(self):
        """创建左侧边栏"""
        content_container = tk.Frame(self.main_container, bg=COLORS["dark_bg"])
        content_container.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        
        # 侧边栏
        sidebar = ModernFrame(content_container, bg=COLORS["card_bg"], 
                            width=200, highlightthickness=0)
        sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        
        # API密钥设置
        api_setting = tk.Frame(sidebar.content_frame, bg=COLORS["card_bg"], pady=10)
        api_setting.pack(fill=tk.X)
        
        api_header = tk.Frame(api_setting, bg=COLORS["card_bg"])
        api_header.pack(fill=tk.X)
        
        tk.Label(api_header, image=self.icons["api"], bg=COLORS["card_bg"]).pack(side=tk.LEFT)
        tk.Label(api_header, text="API设置", font=self.fonts["subheading"],
               bg=COLORS["card_bg"], fg=COLORS["light_text"]).pack(side=tk.LEFT, padx=5)
        
        api_content = tk.Frame(api_setting, bg=COLORS["card_bg"], pady=10)
        api_content.pack(fill=tk.X)
        
        tk.Label(api_content, text="API密钥:", font=self.fonts["small"],
               bg=COLORS["card_bg"], fg=COLORS["grey_text"]).pack(anchor=tk.W)
        
        key_container = tk.Frame(api_content, bg=COLORS["card_bg"], pady=5)
        key_container.pack(fill=tk.X)
        
        self.api_key_var = tk.StringVar()
        self.api_key_entry = tk.Entry(key_container, textvariable=self.api_key_var,
                                    show="*", bg=COLORS["input_bg"], fg=COLORS["dark_text"],
                                    insertbackground=COLORS["primary"],
                                    relief=tk.FLAT, bd=0)
        self.api_key_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5, padx=(0, 5))
        
        # 保存按钮
        save_key_btn = ModernButton(api_content, "保存API密钥", self.save_api_key,
                                  width=180, bg_color=COLORS["primary"])
        save_key_btn.pack(pady=10)
        
        # 分隔线
        self.create_separator(sidebar.content_frame)
        
        # 功能区域
        tools_frame = tk.Frame(sidebar.content_frame, bg=COLORS["card_bg"], pady=10)
        tools_frame.pack(fill=tk.X)
        
        tk.Label(tools_frame, text="功能列表", font=self.fonts["subheading"],
               bg=COLORS["card_bg"], fg=COLORS["light_text"]).pack(anchor=tk.W)
        
        # 功能按钮
        tools = [
            ("智能命名", self.suggest_name, COLORS["primary"]),
            ("智能分类", self.categorize, COLORS["primary"]),
            ("文件分析", self.analyze_file, COLORS["accent"])
        ]
        
        for text, command, color in tools:
            btn = ModernButton(tools_frame, text, command, 
                             width=180, bg_color=color, 
                             height=32, corner_radius=16)
            btn.pack(pady=5)
    
    def create_main_content(self):
        """创建主内容区域"""
        self.content_area = tk.Frame(self.main_container, bg=COLORS["dark_bg"])
        self.content_area.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        
        # 输入区域
        input_frame = ModernFrame(self.content_area, title="输入信息")
        input_frame.pack(fill=tk.X, pady=(0, 15))
        
        input_content = tk.Frame(input_frame.content_frame, bg=COLORS["dark_bg"])
        input_content.pack(fill=tk.X)
        
        # URL输入
        url_container = tk.Frame(input_content, bg=COLORS["dark_bg"], pady=5)
        url_container.pack(fill=tk.X)
        
        tk.Label(url_container, image=self.icons["url"], 
               bg=COLORS["dark_bg"]).pack(side=tk.LEFT)
        tk.Label(url_container, text="下载URL:", font=self.fonts["body"],
               bg=COLORS["dark_bg"], fg=COLORS["light_text"]).pack(side=tk.LEFT, padx=5)
        
        self.url_var = tk.StringVar()
        url_entry = tk.Entry(url_container, textvariable=self.url_var,
                           bg=COLORS["input_bg"], fg=COLORS["dark_text"],
                           insertbackground=COLORS["primary"],
                           relief=tk.FLAT, bd=0)
        url_entry.pack(fill=tk.X, expand=True, ipady=5, padx=5)
        
        # 文件名输入
        filename_container = tk.Frame(input_content, bg=COLORS["dark_bg"], pady=5)
        filename_container.pack(fill=tk.X)
        
        tk.Label(filename_container, image=self.icons["file"], 
               bg=COLORS["dark_bg"]).pack(side=tk.LEFT)
        tk.Label(filename_container, text="原始文件名:", font=self.fonts["body"],
               bg=COLORS["dark_bg"], fg=COLORS["light_text"]).pack(side=tk.LEFT, padx=5)
        
        self.filename_var = tk.StringVar()
        filename_entry = tk.Entry(filename_container, textvariable=self.filename_var,
                                bg=COLORS["input_bg"], fg=COLORS["dark_text"],
                                insertbackground=COLORS["primary"],
                                relief=tk.FLAT, bd=0)
        filename_entry.pack(fill=tk.X, expand=True, ipady=5, padx=5)
        
        # 文件路径输入 (用于文件分析)
        file_container = tk.Frame(input_content, bg=COLORS["dark_bg"], pady=5)
        file_container.pack(fill=tk.X)
        
        tk.Label(file_container, image=self.icons["file"], 
               bg=COLORS["dark_bg"]).pack(side=tk.LEFT)
        tk.Label(file_container, text="文件路径:", font=self.fonts["body"],
               bg=COLORS["dark_bg"], fg=COLORS["light_text"]).pack(side=tk.LEFT, padx=5)
        
        self.file_path_var = tk.StringVar()
        file_path_entry = tk.Entry(file_container, textvariable=self.file_path_var,
                                 bg=COLORS["input_bg"], fg=COLORS["dark_text"],
                                 insertbackground=COLORS["primary"],
                                 relief=tk.FLAT, bd=0)
        file_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5, padx=5)
        
        browse_btn = ModernButton(file_container, "浏览", self.browse_file,
                                width=80, bg_color=COLORS["primary_dark"], height=28)
        browse_btn.pack(side=tk.RIGHT, padx=5)
        
        # 结果显示区域
        result_frame = ModernFrame(self.content_area, title="AI分析结果")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 结果标题
        result_header = tk.Frame(result_frame.content_frame, bg=COLORS["dark_bg"])
        result_header.pack(fill=tk.X)
        
        tk.Label(result_header, image=self.icons["result"], 
               bg=COLORS["dark_bg"]).pack(side=tk.LEFT)
        tk.Label(result_header, text="分析结果", font=self.fonts["body"],
               bg=COLORS["dark_bg"], fg=COLORS["light_text"]).pack(side=tk.LEFT, padx=5)
        
        # 结果文本框和滚动条
        text_container = tk.Frame(result_frame.content_frame, bg=COLORS["dark_bg"], pady=10)
        text_container.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(text_container, wrap=tk.WORD,
                                 bg=COLORS["card_bg"], fg=COLORS["light_text"],
                                 insertbackground=COLORS["primary"],
                                 padx=10, pady=10, bd=0, relief=tk.FLAT,
                                 font=self.fonts["body"])
        scrollbar = ttk.Scrollbar(text_container, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # 添加默认提示文字
        self.result_text.insert(tk.END, "请输入URL和文件名，然后使用左侧功能按钮进行智能分析...\n")
        self.result_text.config(state=tk.DISABLED)  # 设为只读
    
    def create_statusbar(self):
        """创建底部状态栏"""
        statusbar = tk.Frame(self.main_container, bg=COLORS["card_bg"], height=30)
        statusbar.pack(side=tk.BOTTOM, fill=tk.X, pady=(15, 0))
        
        # 状态文本
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        
        status_label = tk.Label(statusbar, textvariable=self.status_var,
                              bg=COLORS["card_bg"], fg=COLORS["grey_text"],
                              font=self.fonts["small"], anchor=tk.W, padx=10, pady=5)
        status_label.pack(side=tk.LEFT, fill=tk.X)
        
        # 进度条
        self.progress_bar = ModernProgressBar(statusbar, width=200, height=6)
        self.progress_bar.pack(side=tk.RIGHT, padx=10, pady=12)
        self.progress_bar.set_progress(0)
    
    def create_separator(self, parent, pady=10):
        """创建分隔线"""
        separator = tk.Frame(parent, height=1, bg=COLORS["border"])
        separator.pack(fill=tk.X, pady=pady)
        return separator
    
    def check_api_key(self):
        """检查API密钥状态"""
        if self.assistant.api_key:
            self.api_key_var.set("*" * 10)  # 显示占位符而不是实际密钥
            self.status_var.set("API密钥已设置")
            self.progress_bar.set_progress(100)
        else:
            self.status_var.set("请设置DeepSeek API密钥")
            self.progress_bar.set_progress(0)
    
    def save_api_key(self):
        """保存API密钥"""
        api_key = self.api_key_var.get().strip()
        if not api_key:
            self.show_error("错误", "API密钥不能为空")
            return
        
        # 显示进度动画
        self.progress_bar.set_progress(30)
        self.status_var.set("正在保存API密钥...")
        
        # 在后台线程中保存API密钥
        def save_key_thread():
            success = self.assistant.set_api_key(api_key)
            
            if success:
                self.root.after(0, lambda: self.status_var.set("API密钥保存成功"))
                self.root.after(0, lambda: self.progress_bar.set_progress(100))
                self.root.after(0, lambda: self.show_success("成功", "API密钥保存成功"))
            else:
                self.root.after(0, lambda: self.status_var.set("API密钥保存失败"))
                self.root.after(0, lambda: self.progress_bar.set_progress(0))
                self.root.after(0, lambda: self.show_error("错误", "API密钥保存失败"))
        
        threading.Thread(target=save_key_thread, daemon=True).start()
    
    def suggest_name(self):
        """智能命名建议"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            self.show_error("错误", "URL和文件名不能为空")
            return
        
        # 显示进度动画
        self.progress_bar.set_progress(20)
        self.status_var.set("正在获取智能命名建议...")
        self.enable_result_edit()
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "正在分析...\n")
        self.disable_result_edit()
        
        # 在后台线程中获取建议
        def suggest_thread():
            self.progress_bar.set_progress(50)
            suggested_name = self.assistant.suggest_download_name(url, filename)
            
            if suggested_name:
                self.progress_bar.set_progress(100)
                self.root.after(0, lambda: self.status_var.set("命名建议获取成功"))
                self.root.after(0, lambda: self.update_result(f"原始文件名: {filename}\n\n建议文件名: {suggested_name}"))
            else:
                self.progress_bar.set_progress(0)
                self.root.after(0, lambda: self.status_var.set("命名建议获取失败"))
                self.root.after(0, lambda: self.update_result("无法获取命名建议，请检查API密钥是否正确设置"))
        
        threading.Thread(target=suggest_thread, daemon=True).start()
    
    def categorize(self):
        """智能分类"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            self.show_error("错误", "URL和文件名不能为空")
            return
        
        # 显示进度动画
        self.progress_bar.set_progress(20)
        self.status_var.set("正在获取智能分类...")
        self.enable_result_edit()
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "正在分析...\n")
        self.disable_result_edit()
        
        # 在后台线程中获取分类
        def categorize_thread():
            self.progress_bar.set_progress(50)
            category = self.assistant.categorize_download(url, filename)
            
            if category:
                self.progress_bar.set_progress(100)
                self.root.after(0, lambda: self.status_var.set("分类获取成功"))
                self.root.after(0, lambda: self.update_result(f"文件: {filename}\n\n建议分类: {category}"))
            else:
                self.progress_bar.set_progress(0)
                self.root.after(0, lambda: self.status_var.set("分类获取失败"))
                self.root.after(0, lambda: self.update_result("无法获取分类建议，请检查API密钥是否正确设置"))
        
        threading.Thread(target=categorize_thread, daemon=True).start()
    
    def browse_file(self):
        """浏览文件"""
        from tkinter import filedialog
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)
    
    def analyze_file(self):
        """分析文件"""
        file_path = self.file_path_var.get().strip()
        
        if not file_path or not os.path.exists(file_path):
            self.show_error("错误", "请选择有效的文件")
            return
        
        # 显示进度动画
        self.progress_bar.set_progress(20)
        self.status_var.set("正在分析文件...")
        self.enable_result_edit()
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "正在分析文件内容...\n")
        self.disable_result_edit()
        
        # 在后台线程中分析文件
        def analyze_thread():
            self.progress_bar.set_progress(40)
            time.sleep(0.5)  # 模拟处理时间
            self.progress_bar.set_progress(60)
            
            result = self.assistant.analyze_file_content(Path(file_path))
            
            if result:
                self.progress_bar.set_progress(100)
                self.root.after(0, lambda: self.status_var.set("文件分析完成"))
                self.root.after(0, lambda: self.update_result(f"文件: {os.path.basename(file_path)}\n\n分析结果:\n{result}"))
            else:
                self.progress_bar.set_progress(0)
                self.root.after(0, lambda: self.status_var.set("文件分析失败"))
                self.root.after(0, lambda: self.update_result("无法分析文件，请检查API密钥是否正确设置"))
        
        threading.Thread(target=analyze_thread, daemon=True).start()
    
    def update_result(self, text):
        """更新结果文本框"""
        self.enable_result_edit()
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, text)
        self.disable_result_edit()
    
    def enable_result_edit(self):
        """启用结果文本框编辑"""
        self.result_text.config(state=tk.NORMAL)
    
    def disable_result_edit(self):
        """禁用结果文本框编辑"""
        self.result_text.config(state=tk.DISABLED)
    
    def show_error(self, title, message):
        """显示错误消息"""
        messagebox.showerror(title, message)
    
    def show_success(self, title, message):
        """显示成功消息"""
        messagebox.showinfo(title, message)
    
    def show_settings(self):
        """显示设置对话框"""
        # 这里可以实现设置对话框
        pass
    
    def show_loading_animation(self):
        """显示加载动画"""
        # 先将进度条动画化到50%
        self.progress_bar.set_progress(50)
        self.status_var.set("正在初始化...")
        
        def finish_loading():
            # 完成加载
            self.progress_bar.set_progress(100)
            self.status_var.set("就绪")
            # 检查API状态
            self.check_api_key()
        
        # 延迟1秒完成加载
        self.root.after(1000, finish_loading)

def main():
    """主函数"""
    root = tk.Tk()
    app = DeepSeekIntegrationDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main() 