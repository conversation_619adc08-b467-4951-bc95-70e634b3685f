"""
DeepSeek集成演示 - 改进版本，使用原生Tkinter组件
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox, filedialog
from pathlib import Path
import threading
import logging

# 导入DeepSeek助手
from deepseek_integration import DeepSeekAssistant

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('deepseek_integration.log')
    ]
)

class LabelFrame(tk.Frame):
    """自定义标签框架，替代ttk.LabelFrame"""
    
    def __init__(self, parent, text, **kwargs):
        bg_color = kwargs.pop('bg', '#333333')
        fg_color = kwargs.pop('fg', 'white')
        
        kwargs['bg'] = bg_color
        super().__init__(parent, **kwargs)
        
        # 添加标签
        self.label = tk.Label(self, text=text, bg=bg_color, fg=fg_color, font=('Arial', 11, 'bold'))
        self.label.pack(anchor='w', padx=5, pady=(0, 5))
        
        # 内部框架
        self.inner_frame = tk.Frame(self, bg=bg_color)
        self.inner_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

class DeepSeekIntegrationDemo:
    """DeepSeek集成演示应用"""
    
    def __init__(self, root):
        """初始化应用"""
        self.root = root
        self.root.title("DeepSeek集成演示")
        self.root.geometry("800x600")
        
        # 初始化DeepSeek助手
        self.assistant = DeepSeekAssistant()
        
        # 设置主题
        self.setup_theme()
        
        # 设置UI
        self.setup_ui()
    
    def setup_theme(self):
        """设置应用主题"""
        # 定义颜色
        self.bg_color = "#333333"       # 主背景色
        self.field_bg = "#2A2A2A"       # 输入字段背景色
        self.btn_bg = "#454545"         # 按钮背景色
        self.btn_hover_bg = "#555555"   # 按钮悬停背景色
        self.btn_text_color = "#4287f5" # 按钮文字颜色 - 蓝色
        self.text_color = "white"       # 文本颜色
        
        # 设置根窗口颜色
        self.root.configure(bg=self.bg_color)
    
    def create_button(self, parent, text, command):
        """创建自定义按钮"""
        # 在macOS上，使用Canvas和Label模拟按钮，解决原生按钮样式问题
        frame = tk.Frame(parent, bg=self.btn_bg, bd=1, relief=tk.RAISED)
        
        # 使用Label显示按钮文本
        label = tk.Label(frame, text=text, bg=self.btn_bg, fg=self.btn_text_color,
                       font=("Arial", 10, "bold"), padx=10, pady=5)
        label.pack(fill=tk.BOTH, expand=True)
        
        # 绑定点击和悬停事件
        def on_enter(e):
            frame.config(bg=self.btn_hover_bg)
            label.config(bg=self.btn_hover_bg)
        
        def on_leave(e):
            frame.config(bg=self.btn_bg)
            label.config(bg=self.btn_bg)
        
        def on_click(e):
            command()
        
        # 绑定事件
        frame.bind("<Enter>", on_enter)
        frame.bind("<Leave>", on_leave)
        frame.bind("<Button-1>", on_click)
        label.bind("<Enter>", on_enter)
        label.bind("<Leave>", on_leave)
        label.bind("<Button-1>", on_click)
        
        return frame
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg=self.bg_color, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # API密钥设置框架
        api_frame = LabelFrame(main_frame, text="DeepSeek API设置", bg=self.bg_color, fg=self.text_color)
        api_frame.pack(fill=tk.X, pady=5)
        
        # API密钥输入
        api_container = tk.Frame(api_frame.inner_frame, bg=self.bg_color)
        api_container.pack(fill=tk.X, pady=5)
        
        tk.Label(api_container, text="API密钥:", bg=self.bg_color, fg=self.text_color).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_var = tk.StringVar()
        self.api_key_entry = tk.Entry(api_container, textvariable=self.api_key_var, width=50, show="*",
                                    bg=self.field_bg, fg=self.text_color, insertbackground=self.text_color)
        self.api_key_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        save_key_btn = self.create_button(api_container, "保存API密钥", self.save_api_key)
        save_key_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # 下载URL框架
        url_frame = LabelFrame(main_frame, text="下载信息", bg=self.bg_color, fg=self.text_color)
        url_frame.pack(fill=tk.X, pady=5)
        
        # URL和文件名输入
        url_container = tk.Frame(url_frame.inner_frame, bg=self.bg_color)
        url_container.pack(fill=tk.X, pady=5)
        
        tk.Label(url_container, text="下载URL:", bg=self.bg_color, fg=self.text_color).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.url_var = tk.StringVar()
        url_entry = tk.Entry(url_container, textvariable=self.url_var, width=70,
                           bg=self.field_bg, fg=self.text_color, insertbackground=self.text_color)
        url_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        tk.Label(url_container, text="原始文件名:", bg=self.bg_color, fg=self.text_color).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.filename_var = tk.StringVar()
        filename_entry = tk.Entry(url_container, textvariable=self.filename_var, width=70,
                                bg=self.field_bg, fg=self.text_color, insertbackground=self.text_color)
        filename_entry.grid(row=1, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 按钮容器
        btn_container = tk.Frame(url_frame.inner_frame, bg=self.bg_color)
        btn_container.pack(fill=tk.X, pady=5)
        
        name_btn = self.create_button(btn_container, "智能命名", self.suggest_name)
        name_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        category_btn = self.create_button(btn_container, "智能分类", self.categorize)
        category_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 结果框架
        result_frame = LabelFrame(main_frame, text="AI分析结果", bg=self.bg_color, fg=self.text_color)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框和滚动条
        result_container = tk.Frame(result_frame.inner_frame, bg=self.bg_color)
        result_container.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(result_container, wrap=tk.WORD, width=80, height=15,
                                 bg=self.field_bg, fg=self.text_color, insertbackground=self.text_color)
        scrollbar = tk.Scrollbar(result_container, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 文件分析框架
        file_frame = LabelFrame(main_frame, text="文件分析", bg=self.bg_color, fg=self.text_color)
        file_frame.pack(fill=tk.X, pady=5)
        
        # 文件路径输入
        file_container = tk.Frame(file_frame.inner_frame, bg=self.bg_color)
        file_container.pack(fill=tk.X, pady=5)
        
        tk.Label(file_container, text="文件路径:", bg=self.bg_color, fg=self.text_color).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.file_path_var = tk.StringVar()
        file_path_entry = tk.Entry(file_container, textvariable=self.file_path_var, width=70,
                                 bg=self.field_bg, fg=self.text_color, insertbackground=self.text_color)
        file_path_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        browse_btn = self.create_button(file_container, "浏览", self.browse_file)
        browse_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # 分析按钮
        btn_container2 = tk.Frame(file_frame.inner_frame, bg=self.bg_color)
        btn_container2.pack(fill=tk.X, pady=5)
        
        analyze_btn = self.create_button(btn_container2, "分析文件", self.analyze_file)
        analyze_btn.pack(pady=5)
        
        # 状态栏
        status_frame = tk.Frame(main_frame, bg=self.bg_color)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = tk.Label(status_frame, textvariable=self.status_var, 
                            bg=self.field_bg, fg=self.text_color, 
                            relief=tk.SUNKEN, anchor=tk.W, padx=5, pady=2)
        status_bar.pack(fill=tk.X)
        
        # 检查API密钥状态
        self.check_api_key()
    
    def check_api_key(self):
        """检查API密钥状态"""
        if self.assistant.api_key:
            self.api_key_var.set("*" * 10)  # 显示占位符而不是实际密钥
            self.status_var.set("API密钥已设置")
        else:
            self.status_var.set("请设置DeepSeek API密钥")
    
    def save_api_key(self):
        """保存API密钥"""
        api_key = self.api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("错误", "API密钥不能为空")
            return
        
        # 在后台线程中保存API密钥
        def save_key_thread():
            self.status_var.set("正在保存API密钥...")
            success = self.assistant.set_api_key(api_key)
            
            if success:
                self.root.after(0, lambda: self.status_var.set("API密钥保存成功"))
                self.root.after(0, lambda: messagebox.showinfo("成功", "API密钥保存成功"))
            else:
                self.root.after(0, lambda: self.status_var.set("API密钥保存失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", "API密钥保存失败"))
        
        threading.Thread(target=save_key_thread, daemon=True).start()
    
    def suggest_name(self):
        """智能命名建议"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            messagebox.showerror("错误", "URL和文件名不能为空")
            return
        
        # 在后台线程中获取建议
        def suggest_thread():
            self.status_var.set("正在获取智能命名建议...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析...\n")
            
            suggested_name = self.assistant.suggest_download_name(url, filename)
            
            if suggested_name:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"原始文件名: {filename}\n\n建议文件名: {suggested_name}"))
                self.root.after(0, lambda: self.status_var.set("命名建议获取成功"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法获取命名建议，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("命名建议获取失败"))
        
        threading.Thread(target=suggest_thread, daemon=True).start()
    
    def categorize(self):
        """智能分类"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            messagebox.showerror("错误", "URL和文件名不能为空")
            return
        
        # 在后台线程中获取分类
        def categorize_thread():
            self.status_var.set("正在获取智能分类...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析...\n")
            
            category = self.assistant.categorize_download(url, filename)
            
            if category:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"文件: {filename}\n\n建议分类: {category}"))
                self.root.after(0, lambda: self.status_var.set("分类获取成功"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法获取分类建议，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("分类获取失败"))
        
        threading.Thread(target=categorize_thread, daemon=True).start()
    
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)
    
    def analyze_file(self):
        """分析文件"""
        file_path = self.file_path_var.get().strip()
        
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请选择有效的文件")
            return
        
        # 在后台线程中分析文件
        def analyze_thread():
            self.status_var.set("正在分析文件...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析文件内容...\n")
            
            result = self.assistant.analyze_file_content(Path(file_path))
            
            if result:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"文件: {os.path.basename(file_path)}\n\n分析结果:\n{result}"))
                self.root.after(0, lambda: self.status_var.set("文件分析完成"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法分析文件，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("文件分析失败"))
        
        threading.Thread(target=analyze_thread, daemon=True).start()

def main():
    """主函数"""
    root = tk.Tk()
    app = DeepSeekIntegrationDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main() 