'''
Author: hgxszhj <EMAIL>
Date: 2024-05-29 15:56:53
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2025-03-01 07:24:58
FilePath: /my_project/2024-5-20/download_myctu_v01.py
Description: File download utility with configurable settings and error handling
'''
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import requests
import json
import base64
from pathlib import Path
import re
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from colorama import Fore, init
from typing import Dict, Any
import logging
import os
from dotenv import load_dotenv
import threading

init(autoreset=True)

# Load environment variables
load_dotenv()

DEBUG = False

HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Cookie": os.getenv("COOKIE", ""),
    "Authorization": os.getenv("AUTH_TOKEN", "")
}

def create_session():
    session = requests.Session()
    retries = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504]
    )
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

def print_status(message: str, status: str = "info"):
    """统一的状态输出函数"""
    colors = {
        "info": Fore.CYAN,
        "success": Fore.GREEN,
        "error": Fore.RED,
        "warning": Fore.YELLOW
    }
    print(f"{colors.get(status, Fore.WHITE)}{message}")

def download_file(url: str, file_path: str, headers: dict) -> bool:
    """返回布尔值表示下载是否成功"""
    try:
        session = create_session()
        print_status("正在下载文件...", "info")
        response = session.get(url, headers=headers, timeout=(3.05, 30))
        response.raise_for_status()
        
        data = base64.b64decode(response.json()['body'])
        
        with open(file_path, 'wb') as f:
            f.write(data)
        print_status(f"文件已成功保存至：{file_path}", "success")
        return True
        
    except requests.exceptions.RequestException as e:
        print_status(f"网络请求失败: {str(e)}", "error")
    except KeyError:
        print_status("响应数据格式异常，缺少'body'字段", "error")
    except IOError as e:
        print_status(f"文件保存失败: {str(e)}", "error")
    except Exception as e:
        print_status(f"发生未知错误: {str(e)}", "error")
    return False

def sanitize_filename(filename: str) -> str:
    # 移除非法字符并限制文件名长度
    cleaned = re.sub(r'[\\/*?:"<>|]', "", filename)[:100]
    return cleaned or "untitled"

def show_banner():
    print(Fore.CYAN + r"""
     ____  _             _          _   _             
    |  _ \| |_   _  __ _| |__      | | | |___  ___ _ __ 
    | | | | | | | |/ _` | '_ \  _  | | | / __|/ _ \ '__|
    | |_| | | |_| | (_| | | | || | | |_| \__ \  __/ |   
    |____/|_|\__,_|\__,_|_| |_(_)  \___/|___/\___|_|   
    """)

def toggle_debug():
    global DEBUG
    DEBUG = not DEBUG
    print_status(f"调试模式 {'已启用' if DEBUG else '已禁用'}", "warning")

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('download.log'),
            logging.StreamHandler()
        ]
    )

class Config:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config: Dict[str, Any] = self._load_config()
        self.debug = False

    def _load_config(self) -> Dict[str, Any]:
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        default_config = {
            "save_dir": "D:/EXAM/岗位认证",
            "headers": HEADERS,
            "debug": False
        }
        self.save_config(default_config)
        return default_config

    def save_config(self, config: Dict[str, Any]) -> None:
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=4)

    def toggle_debug(self):
        """Toggle debug mode"""
        self.debug = not self.debug
        print_status(f"调试模式 {'已启用' if self.debug else '已禁用'}", "warning")

class DownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件下载器")
        self.root.geometry("600x400")
        self.config = Config()
        self.setup_ui()
        self.setup_logging()

    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('download.log'),
                logging.StreamHandler()
            ]
        )

    def setup_ui(self):
        # URL输入框
        url_frame = ttk.Frame(self.root)
        url_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(url_frame, text="下载链接:").pack(side=tk.LEFT)
        self.url_entry = ttk.Entry(url_frame)
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # 文件名输入框
        name_frame = ttk.Frame(self.root)
        name_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(name_frame, text="文件名:").pack(side=tk.LEFT)
        self.name_entry = ttk.Entry(name_frame)
        self.name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # 保存目录选择
        dir_frame = ttk.Frame(self.root)
        dir_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(dir_frame, text="保存目录:").pack(side=tk.LEFT)
        self.dir_entry = ttk.Entry(dir_frame)
        self.dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.dir_entry.insert(0, self.config.config["save_dir"])
        ttk.Button(dir_frame, text="浏览", command=self.choose_directory).pack(side=tk.LEFT)

        # 进度条
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill=tk.X, padx=5, pady=5)

        # 状态显示
        self.status_text = tk.Text(self.root, height=10, wrap=tk.WORD)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 下载按钮
        self.download_btn = ttk.Button(self.root, text="开始下载", command=self.start_download)
        self.download_btn.pack(pady=10)

    def choose_directory(self):
        directory = filedialog.askdirectory(initialdir=self.dir_entry.get())
        if directory:
            self.dir_entry.delete(0, tk.END)
            self.dir_entry.insert(0, directory)
            self.config.config["save_dir"] = directory
            self.config.save_config(self.config.config)

    def update_status(self, message: str, level: str = "info"):
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        logging.log(
            getattr(logging, level.upper(), logging.INFO),
            message
        )

    def start_download(self):
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入下载链接")
            return
        if not url.startswith(('http://', 'https://')):
            messagebox.showerror("错误", "链接格式不正确，必须以http://或https://开头")
            return

        raw_name = self.name_entry.get().strip()
        if not raw_name:
            messagebox.showerror("错误", "请输入文件名")
            return

        safe_name = sanitize_filename(raw_name)
        save_dir = Path(self.dir_entry.get())
        save_dir.mkdir(parents=True, exist_ok=True)
        file_path = save_dir / f"{safe_name}.pdf"

        if file_path.exists():
            if not messagebox.askyesno("确认", "文件已存在，是否覆盖？"):
                self.update_status("已取消下载", "warning")
                return

        self.download_btn.state(['disabled'])
        self.progress.start()
        
        # 在新线程中执行下载
        thread = threading.Thread(target=self.download_thread, args=(url, file_path))
        thread.start()

    def download_thread(self, url: str, file_path: Path):
        try:
            session = create_session()
            self.update_status("正在下载文件...", "info")
            response = session.get(url, headers=HEADERS, timeout=(3.05, 30))
            response.raise_for_status()
            
            data = base64.b64decode(response.json()['body'])
            
            with open(file_path, 'wb') as f:
                f.write(data)
            self.update_status(f"文件已成功保存至：{file_path}", "info")
            messagebox.showinfo("成功", "下载完成！")
            
        except Exception as e:
            self.update_status(f"下载失败: {str(e)}", "error")
            messagebox.showerror("错误", f"下载失败: {str(e)}")
        finally:
            self.root.after(0, self.finish_download)

    def finish_download(self):
        self.progress.stop()
        self.download_btn.state(['!disabled'])

def main():
    root = tk.Tk()
    app = DownloaderGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main()
