"""
DeepSeek集成示例 - 展示如何将DeepSeek助手集成到下载器应用程序中
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import threading
import logging

# 导入DeepSeek助手
from deepseek_integration import DeepSeekAssistant

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('deepseek_integration.log')
    ]
)

class DeepSeekIntegrationDemo:
    """DeepSeek集成演示应用"""
    
    def __init__(self, root):
        """初始化应用"""
        self.root = root
        self.root.title("DeepSeek集成演示")
        self.root.geometry("800x600")
        
        # 初始化DeepSeek助手
        self.assistant = DeepSeekAssistant()
        
        # 设置主题和样式
        self.setup_theme()
        
        # 设置UI
        self.setup_ui()
    
    def setup_theme(self):
        """设置应用主题和样式"""
        # 设置深色主题
        self.root.configure(bg="#333333")
        
        # 创建自定义样式
        style = ttk.Style()
        style.configure("TFrame", background="#333333")
        style.configure("TLabelframe", background="#333333", foreground="white")
        style.configure("TLabelframe.Label", background="#333333", foreground="white")
        style.configure("TLabel", background="#333333", foreground="white")
        style.configure("TButton", background="#555555", foreground="white")
        style.configure("TEntry", fieldbackground="#555555", foreground="white")
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # API密钥设置框架
        api_frame = ttk.LabelFrame(main_frame, text="DeepSeek API设置", padding="5")
        api_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(api_frame, text="API密钥:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_var = tk.StringVar()
        self.api_key_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, width=50, show="*")
        self.api_key_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Button(api_frame, text="保存API密钥", command=self.save_api_key).grid(row=0, column=2, padx=5, pady=5)
        
        # 下载URL框架
        url_frame = ttk.LabelFrame(main_frame, text="下载信息", padding="5")
        url_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(url_frame, text="下载URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.url_var = tk.StringVar()
        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=70)
        url_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        ttk.Label(url_frame, text="原始文件名:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.filename_var = tk.StringVar()
        filename_entry = ttk.Entry(url_frame, textvariable=self.filename_var, width=70)
        filename_entry.grid(row=1, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        ttk.Button(url_frame, text="智能命名", command=self.suggest_name).grid(row=2, column=0, padx=5, pady=5)
        ttk.Button(url_frame, text="智能分类", command=self.categorize).grid(row=2, column=1, padx=5, pady=5)
        
        # 结果框架
        result_frame = ttk.LabelFrame(main_frame, text="AI分析结果", padding="5")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框和滚动条
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, width=80, height=15,
                                 bg="#2A2A2A", fg="white", insertbackground="white")
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 文件分析框架
        file_frame = ttk.LabelFrame(main_frame, text="文件分析", padding="5")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(file_frame, text="文件路径:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.file_path_var = tk.StringVar()
        file_path_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=70)
        file_path_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(file_frame, text="分析文件", command=self.analyze_file).grid(row=1, column=1, padx=5, pady=5)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 修复Entry的颜色问题 (因为ttk样式有限制)
        self._fix_entry_colors(url_entry, "#2A2A2A", "white")
        self._fix_entry_colors(filename_entry, "#2A2A2A", "white")
        self._fix_entry_colors(file_path_entry, "#2A2A2A", "white")
        self._fix_entry_colors(self.api_key_entry, "#2A2A2A", "white")
        
        # 检查API密钥状态
        self.check_api_key()
    
    def _fix_entry_colors(self, entry_widget, bg_color, fg_color):
        """修复Entry部件的颜色"""
        entry_widget.config(style="")  # 移除ttk样式
        entry_widget['background'] = bg_color
        entry_widget['foreground'] = fg_color
        
    def check_api_key(self):
        """检查API密钥状态"""
        if self.assistant.api_key:
            self.api_key_var.set("*" * 10)  # 显示占位符而不是实际密钥
            self.status_var.set("API密钥已设置")
        else:
            self.status_var.set("请设置DeepSeek API密钥")
    
    def save_api_key(self):
        """保存API密钥"""
        api_key = self.api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("错误", "API密钥不能为空")
            return
        
        # 在后台线程中保存API密钥
        def save_key_thread():
            self.status_var.set("正在保存API密钥...")
            success = self.assistant.set_api_key(api_key)
            
            if success:
                self.root.after(0, lambda: self.status_var.set("API密钥保存成功"))
                self.root.after(0, lambda: messagebox.showinfo("成功", "API密钥保存成功"))
            else:
                self.root.after(0, lambda: self.status_var.set("API密钥保存失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", "API密钥保存失败"))
        
        threading.Thread(target=save_key_thread, daemon=True).start()
    
    def suggest_name(self):
        """智能命名建议"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            messagebox.showerror("错误", "URL和文件名不能为空")
            return
        
        # 在后台线程中获取建议
        def suggest_thread():
            self.status_var.set("正在获取智能命名建议...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析...\n")
            
            suggested_name = self.assistant.suggest_download_name(url, filename)
            
            if suggested_name:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"原始文件名: {filename}\n\n建议文件名: {suggested_name}"))
                self.root.after(0, lambda: self.status_var.set("命名建议获取成功"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法获取命名建议，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("命名建议获取失败"))
        
        threading.Thread(target=suggest_thread, daemon=True).start()
    
    def categorize(self):
        """智能分类"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            messagebox.showerror("错误", "URL和文件名不能为空")
            return
        
        # 在后台线程中获取分类
        def categorize_thread():
            self.status_var.set("正在获取智能分类...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析...\n")
            
            category = self.assistant.categorize_download(url, filename)
            
            if category:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"文件: {filename}\n\n建议分类: {category}"))
                self.root.after(0, lambda: self.status_var.set("分类获取成功"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法获取分类建议，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("分类获取失败"))
        
        threading.Thread(target=categorize_thread, daemon=True).start()
    
    def browse_file(self):
        """浏览文件"""
        from tkinter import filedialog
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)
    
    def analyze_file(self):
        """分析文件"""
        file_path = self.file_path_var.get().strip()
        
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请选择有效的文件")
            return
        
        # 在后台线程中分析文件
        def analyze_thread():
            self.status_var.set("正在分析文件...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析文件内容...\n")
            
            result = self.assistant.analyze_file_content(Path(file_path))
            
            if result:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"文件: {os.path.basename(file_path)}\n\n分析结果:\n{result}"))
                self.root.after(0, lambda: self.status_var.set("文件分析完成"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法分析文件，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("文件分析失败"))
        
        threading.Thread(target=analyze_thread, daemon=True).start()

def main():
    """主函数"""
    root = tk.Tk()
    app = DeepSeekIntegrationDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main() 