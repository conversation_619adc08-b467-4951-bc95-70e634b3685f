"""
DeepSeek集成示例 - 展示如何将DeepSeek助手集成到下载器应用程序中
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import threading
import logging

# 导入DeepSeek助手
from deepseek_integration import DeepSeekAssistant

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('deepseek_integration.log')
    ]
)

class LabelFrame(tk.Frame):
    """自定义标签框架，替代ttk.LabelFrame"""
    
    def __init__(self, parent, text, **kwargs):
        bg_color = kwargs.pop('bg', '#333333')
        fg_color = kwargs.pop('fg', '#00AA55')  # 改为深绿色以保持一致性
        
        kwargs['bg'] = bg_color
        super().__init__(parent, **kwargs)
        
        # 添加标签
        self.label = tk.Label(self, text=text, bg=bg_color, fg=fg_color, font=('Arial', 11, 'bold'))
        self.label.pack(anchor='w', padx=5, pady=(0, 5))
        
        # 内部框架
        self.inner_frame = tk.Frame(self, bg=bg_color)
        self.inner_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

class DeepSeekIntegrationDemo:
    """DeepSeek集成演示应用"""
    
    def __init__(self, root):
        """初始化应用"""
        self.root = root
        self.root.title("DeepSeek集成演示")
        self.root.geometry("800x600")
        
        # 初始化DeepSeek助手
        self.assistant = DeepSeekAssistant()
        
        # 设置主题
        self.setup_theme()
        
        # 设置UI
        self.setup_ui()
    
    def setup_theme(self):
        """设置应用主题和样式"""
        # 设置深色主题
        self.root.configure(bg="#333333")
        
        # 统一使用的颜色
        self.bg_color = "#333333"       # 主背景色
        self.field_bg = "#2A2A2A"       # 输入字段背景色
        self.btn_bg = "#3A7EBF"         # 按钮背景色
        self.btn_hover_bg = "#2069A9"   # 按钮悬停背景色
        self.text_color = "#00AA55"     # 深绿色文本
        
        # 创建自定义样式
        style = ttk.Style()
        style.configure("TFrame", background=self.bg_color)
        style.configure("TLabelframe", background=self.bg_color, foreground=self.text_color)
        style.configure("TLabelframe.Label", background=self.bg_color, foreground=self.text_color)
        style.configure("TLabel", background=self.bg_color, foreground=self.text_color)
        
        # 修改按钮样式为深色背景白色字体
        style.configure("TButton", background="#444444", foreground=self.text_color)
        # 创建自定义按钮样式
        style.configure("Custom.TButton", background=self.btn_bg, foreground=self.text_color, font=('Arial', 10, 'bold'))
        # 设置按钮悬停效果
        style.map("Custom.TButton",
            background=[('active', self.btn_hover_bg), ('pressed', '#1B5E99')],
            foreground=[('active', self.text_color), ('pressed', self.text_color)])
            
        style.configure("TEntry", fieldbackground=self.field_bg, foreground=self.text_color)
    
    def create_button(self, parent, text, command):
        """创建自定义按钮"""
        # 使用Label模拟按钮，确保颜色一致性
        button = tk.Label(
            parent, 
            text=text,
            bg=self.btn_bg,
            fg=self.text_color,
            font=("Arial", 11, "bold"),
            padx=15,
            pady=7,
            relief=tk.RAISED,
            bd=2,
            cursor="hand2"  # 鼠标悬停时显示手形光标
        )
        
        # 高亮边框
        button.configure(highlightbackground=self.text_color, highlightthickness=1)
        
        # 绑定点击和悬停事件
        def on_enter(e):
            button.config(bg=self.btn_hover_bg)
        
        def on_leave(e):
            button.config(bg=self.btn_bg)
        
        def on_click(e):
            # 点击动画效果
            button.config(relief=tk.SUNKEN)
            parent.after(100, lambda: button.config(relief=tk.RAISED))
            command()
        
        # 绑定事件
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        button.bind("<Button-1>", on_click)
        
        return button
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # API密钥设置框架
        api_frame = ttk.LabelFrame(main_frame, text="DeepSeek API设置", padding="5")
        api_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(api_frame, text="API密钥:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_var = tk.StringVar()
        self.api_key_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, width=50, show="*")
        self.api_key_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 使用原生tk按钮替代ttk按钮
        save_key_btn = tk.Button(api_frame, text="保存API密钥", command=self.save_api_key, 
                               bg="#3A7EBF", fg=self.text_color, font=("Arial", 10, "bold"),
                               activebackground="#2069A9", activeforeground=self.text_color)
        save_key_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # 下载URL框架
        url_frame = ttk.LabelFrame(main_frame, text="下载信息", padding="5")
        url_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(url_frame, text="下载URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.url_var = tk.StringVar()
        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=70)
        url_entry.grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        ttk.Label(url_frame, text="原始文件名:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.filename_var = tk.StringVar()
        filename_entry = ttk.Entry(url_frame, textvariable=self.filename_var, width=70)
        filename_entry.grid(row=1, column=1, columnspan=2, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 使用原生tk按钮
        name_btn = tk.Button(url_frame, text="智能命名", command=self.suggest_name,
                           bg="#3A7EBF", fg=self.text_color, font=("Arial", 10, "bold"),
                           activebackground="#2069A9", activeforeground=self.text_color)
        name_btn.grid(row=2, column=0, padx=5, pady=5)
        
        category_btn = tk.Button(url_frame, text="智能分类", command=self.categorize,
                               bg="#3A7EBF", fg=self.text_color, font=("Arial", 10, "bold"),
                               activebackground="#2069A9", activeforeground=self.text_color)
        category_btn.grid(row=2, column=1, padx=5, pady=5)
        
        # 结果框架
        result_frame = ttk.LabelFrame(main_frame, text="AI分析结果", padding="5")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框和滚动条
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, width=80, height=15,
                                 bg="#2A2A2A", fg=self.text_color, insertbackground=self.text_color)
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 文件分析框架
        file_frame = ttk.LabelFrame(main_frame, text="文件分析", padding="5")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(file_frame, text="文件路径:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.file_path_var = tk.StringVar()
        file_path_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=70)
        file_path_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        # 使用原生tk按钮
        browse_btn = tk.Button(file_frame, text="浏览", command=self.browse_file,
                             bg="#3A7EBF", fg=self.text_color, font=("Arial", 10, "bold"),
                             activebackground="#2069A9", activeforeground=self.text_color)
        browse_btn.grid(row=0, column=2, padx=5, pady=5)
        
        analyze_btn = tk.Button(file_frame, text="分析文件", command=self.analyze_file,
                              bg="#3A7EBF", fg=self.text_color, font=("Arial", 10, "bold"),
                              activebackground="#2069A9", activeforeground=self.text_color)
        analyze_btn.grid(row=1, column=1, padx=5, pady=5)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 修复Entry的颜色问题 (因为ttk样式有限制)
        self._fix_entry_colors(url_entry, "#2A2A2A", self.text_color)
        self._fix_entry_colors(filename_entry, "#2A2A2A", self.text_color)
        self._fix_entry_colors(file_path_entry, "#2A2A2A", self.text_color)
        self._fix_entry_colors(self.api_key_entry, "#2A2A2A", self.text_color)
        
        # 检查API密钥状态
        self.check_api_key()
    
    def check_api_key(self):
        """检查API密钥状态"""
        if self.assistant.api_key:
            self.api_key_var.set("*" * 10)  # 显示占位符而不是实际密钥
            self.status_var.set("API密钥已设置")
        else:
            self.status_var.set("请设置DeepSeek API密钥")
    
    def _fix_entry_colors(self, entry_widget, bg_color, fg_color):
        """修复Entry部件的颜色"""
        entry_widget.config(style="")  # 移除ttk样式
        entry_widget['background'] = bg_color
        entry_widget['foreground'] = fg_color
    
    def save_api_key(self):
        """保存API密钥"""
        api_key = self.api_key_var.get().strip()
        if not api_key:
            messagebox.showerror("错误", "API密钥不能为空")
            return
        
        # 在后台线程中保存API密钥
        def save_key_thread():
            self.status_var.set("正在保存API密钥...")
            success = self.assistant.set_api_key(api_key)
            
            if success:
                self.root.after(0, lambda: self.status_var.set("API密钥保存成功"))
                self.root.after(0, lambda: messagebox.showinfo("成功", "API密钥保存成功"))
            else:
                self.root.after(0, lambda: self.status_var.set("API密钥保存失败"))
                self.root.after(0, lambda: messagebox.showerror("错误", "API密钥保存失败"))
        
        threading.Thread(target=save_key_thread, daemon=True).start()
    
    def suggest_name(self):
        """智能命名建议"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            messagebox.showerror("错误", "URL和文件名不能为空")
            return
        
        # 在后台线程中获取建议
        def suggest_thread():
            self.status_var.set("正在获取智能命名建议...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析...\n")
            
            suggested_name = self.assistant.suggest_download_name(url, filename)
            
            if suggested_name:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"原始文件名: {filename}\n\n建议文件名: {suggested_name}"))
                self.root.after(0, lambda: self.status_var.set("命名建议获取成功"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法获取命名建议，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("命名建议获取失败"))
        
        threading.Thread(target=suggest_thread, daemon=True).start()
    
    def categorize(self):
        """智能分类"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        
        if not url or not filename:
            messagebox.showerror("错误", "URL和文件名不能为空")
            return
        
        # 在后台线程中获取分类
        def categorize_thread():
            self.status_var.set("正在获取智能分类...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析...\n")
            
            category = self.assistant.categorize_download(url, filename)
            
            if category:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"文件: {filename}\n\n建议分类: {category}"))
                self.root.after(0, lambda: self.status_var.set("分类获取成功"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法获取分类建议，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("分类获取失败"))
        
        threading.Thread(target=categorize_thread, daemon=True).start()
    
    def browse_file(self):
        """浏览文件"""
        from tkinter import filedialog
        file_path = filedialog.askopenfilename()
        if file_path:
            self.file_path_var.set(file_path)
    
    def analyze_file(self):
        """分析文件"""
        file_path = self.file_path_var.get().strip()
        
        if not file_path or not os.path.exists(file_path):
            messagebox.showerror("错误", "请选择有效的文件")
            return
        
        # 在后台线程中分析文件
        def analyze_thread():
            self.status_var.set("正在分析文件...")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在分析文件内容...\n")
            
            result = self.assistant.analyze_file_content(Path(file_path))
            
            if result:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"文件: {os.path.basename(file_path)}\n\n分析结果:\n{result}"))
                self.root.after(0, lambda: self.status_var.set("文件分析完成"))
            else:
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, "无法分析文件，请检查API密钥是否正确设置"))
                self.root.after(0, lambda: self.status_var.set("文件分析失败"))
        
        threading.Thread(target=analyze_thread, daemon=True).start()

def main():
    """主函数"""
    root = tk.Tk()
    app = DeepSeekIntegrationDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main() 