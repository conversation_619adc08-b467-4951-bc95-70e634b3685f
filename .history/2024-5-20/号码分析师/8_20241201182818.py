import re  # 导入正则表达式模块
import json  # 导入JSON处理模块
import ttkbootstrap as ttk  # 导入ttkbootstrap库用于美化Tkinter界面
from ttkbootstrap.constants import *  # 导入ttkbootstrap常量
from tkinter import StringVar, messagebox, Text, Scrollbar, VERTICAL, END, OptionMenu, PhotoImage  # 从Tkinter导入相关组件
import csv  # 导入CSV模块
from functools import wraps  # 导入wraps装饰器用于保持函数元数据
import requests  # 导入requests库用于HTTP请求
import logging
import os
from pathlib import Path
import sys

# 加载配置文件
def load_config():
    config_path = Path(__file__).parent / "config.json"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        messagebox.showerror("配置错误", f"无法加载配置文件: {e}")
        return {}

# 设置日志
def setup_logging(config):
    log_file = config.get("logging", {}).get("log_file", "phone_analyzer.log")
    log_level = config.get("logging", {}).get("log_level", "INFO")
    
    logging.basicConfig(
        filename=log_file,
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def dynamic_evaluation_method(method_name):
    """
    Decorator to dynamically add evaluation methods to PhoneNumberEvaluator.
    动态添加评估方法的装饰器
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            return func(self, *args, **kwargs)  # 调用被装饰的函数
        setattr(PhoneNumberEvaluator, method_name, wrapper)  # 将装饰后的函数绑定到PhoneNumberEvaluator类中
        return wrapper
    return decorator


class PhoneNumberEvaluator:
    def __init__(self, phone_number, analysis_file):
        self.phone_number = phone_number
        self.score = 0
        self.analysis_data = self.load_analysis_data(analysis_file)
        
    def load_analysis_data(self, file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"无法加载分析数据：{str(e)}")

    def is_valid_number(self, number):
        return bool(re.match(r'^1[3-9]\d{9}$', number))

    def get_carrier_info(self):
        prefix = self.phone_number[:3]
        return self.analysis_data.get("carrier_info", {}).get(prefix, {})

    def analyze_pattern(self):
        # 分析数字模式
        last_8_digits = self.phone_number[-8:]
        patterns = []
        
        # 检查特殊序列
        for pattern_name, pattern_info in self.analysis_data["patterns"]["special_sequences"].items():
            if self._matches_pattern(last_8_digits, pattern_name):
                patterns.append({
                    "type": "special_sequence",
                    "name": pattern_name,
                    "score": pattern_info["score"],
                    "description": pattern_info["description"]
                })
                self.score += pattern_info["score"]
        
        # 检查吉利组合
        for combo in self.analysis_data["patterns"]["lucky_combinations"]:
            if combo in last_8_digits:
                patterns.append({
                    "type": "lucky_combination",
                    "value": combo,
                    "score": self.analysis_data["scoring"]["pattern_bonuses"]["lucky_combination"],
                    "description": "吉利数字组合"
                })
                self.score += self.analysis_data["scoring"]["pattern_bonuses"]["lucky_combination"]
        
        return patterns

    def _matches_pattern(self, number_str, pattern_type):
        if pattern_type == "AAAA":
            return bool(re.search(r'(\d)\1{3}', number_str))
        elif pattern_type == "AABB":
            return bool(re.search(r'(\d)\1(\d)\2', number_str))
        elif pattern_type == "ABCD":
            return bool(re.search(r'(?:0123|1234|2345|3456|7890)', number_str))
        elif pattern_type == "DCBA":
            return bool(re.search(r'(?:3210|4321|5432|6543|9876)', number_str))
        elif pattern_type == "ABAB":
            return bool(re.search(r'(\d)(\d)\1\2', number_str))
        return False

    def calculate_numerology(self):
        # 计算数字根
        num_root = sum(int(d) for d in self.phone_number) % 9 or 9
        numerology_info = self.analysis_data.get("numerology_info", {}).get(str(num_root), {})
        
        return {
            "root_number": num_root,
            "nature": numerology_info.get("nature", ""),
            "characteristics": numerology_info.get("characteristics", ""),
            "career": numerology_info.get("career", ""),
            "relationships": numerology_info.get("relationships", ""),
            "lucky_elements": numerology_info.get("lucky_elements", []),
            "lucky_colors": numerology_info.get("lucky_colors", [])
        }

    def calculate_value(self):
        # 基础分数
        base_value = self.score * 100
        
        # 获取市场趋势调整
        market_trends = self.analysis_data["market_trends"]
        trend_multiplier = market_trends["standard"]
        
        # 检查是否有特殊模式的市场趋势
        for pattern_type, multiplier in market_trends.items():
            if pattern_type != "standard" and self._matches_pattern(self.phone_number[-8:], pattern_type):
                trend_multiplier = multiplier
                break
        
        # 应用价值因子
        factors = self.analysis_data["value_factors"]
        final_value = base_value * (
            factors["pattern_quality"] * trend_multiplier +
            factors["numerology_score"] * 0.8 +
            factors["carrier_premium"] * 1.2 +
            factors["market_trend"]
        )
        
        return round(final_value, 2)

    def evaluate(self):
        if not self.is_valid_number(self.phone_number):
            raise ValueError("无效的手机号码格式")

        # 获取运营商信息
        carrier_info = self.get_carrier_info()
        
        # 分析数字模式
        patterns = self.analyze_pattern()
        
        # 计算数理
        numerology = self.calculate_numerology()
        
        # 计算估值
        value = self.calculate_value()
        
        # 整合分析结果
        result = {
            "phone_number": self.phone_number,
            "score": self.score,
            "estimated_value": value,
            "carrier_info": {
                "operator": carrier_info.get("operator", "未知"),
                "region": carrier_info.get("region", "未知"),
                "network": carrier_info.get("network", "未知")
            },
            "pattern_analysis": patterns,
            "numerology": numerology
        }
        
        return result

class PhoneAnalyzerGUI:
    def __init__(self, root, config):
        self.root = root
        self.config = config
        
        # 获取当前脚本所在目录
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.analysis_data_path = os.path.join(self.current_dir, 'analysis_data.json')
        
        # 设置窗口
        self.setup_window()
        # 设置GUI
        self.setup_gui()
        
    def setup_window(self):
        # 设置窗口标题
        self.root.title("手机号码分析专家系统")
        
        # 设置窗口大小
        window_size = self.config["gui"]["window_size"]
        self.root.geometry(window_size)
        
        # 设置窗口图标
        try:
            icon_path = os.path.join(self.current_dir, "icon.png")
            if os.path.exists(icon_path):
                icon = PhotoImage(file=icon_path)
                self.root.iconphoto(True, icon)
        except Exception as e:
            logging.warning(f"无法加载图标: {str(e)}")
            
    def setup_gui(self):
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=BOTH, expand=True)
        
        # 创建输入和结果区域
        self.create_input_section()
        self.create_result_section()
        
    def create_input_section(self):
        # 创建输入区域框架
        input_frame = ttk.Frame(self.main_frame, padding="10")
        input_frame.pack(fill=X, padx=20, pady=10)

        # 创建标题标签
        title_label = ttk.Label(
            input_frame,
            text="手机号码分析师",
            font=self.config["gui"]["fonts"]["title"]
        )
        title_label.pack(pady=10)

        # 创建输入框架
        entry_frame = ttk.Frame(input_frame)
        entry_frame.pack(fill=X, pady=10)

        # 创建输入标签
        input_label = ttk.Label(
            entry_frame,
            text="请输入手机号码：",
            font=self.config["gui"]["fonts"]["content"]
        )
        input_label.pack(side=LEFT, padx=5)

        # 创建输入框
        self.phone_entry = ttk.Entry(
            entry_frame,
            width=30,
            font=self.config["gui"]["fonts"]["content"]
        )
        self.phone_entry.pack(side=LEFT, padx=5)

        # 创建分析按钮
        analyze_button = ttk.Button(
            input_frame,
            text="开始分析",
            command=self.evaluate_number,
            style="primary.TButton"
        )
        analyze_button.pack(pady=10)

    def create_result_section(self):
        # 创建结果区域框架
        result_frame = ttk.Frame(self.main_frame, padding="10")
        result_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 创建结果文本框
        self.result_text = Text(
            result_frame,
            wrap=WORD,
            height=20,
            font=self.config["gui"]["fonts"]["content"],
            bg=self.config["gui"]["colors"]["primary"],
            fg=self.config["gui"]["colors"]["text"]
        )
        self.result_text.pack(fill=BOTH, expand=True, side=LEFT)

        # 创建滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=VERTICAL, command=self.result_text.yview)
        scrollbar.pack(fill=Y, side=RIGHT)
        self.result_text.configure(yscrollcommand=scrollbar.set)

        # 配置文本标签
        self.result_text.tag_configure(
            "title",
            font=self.config["gui"]["fonts"]["title"],
            foreground=self.config["gui"]["colors"]["secondary"]
        )
        self.result_text.tag_configure(
            "section",
            font=self.config["gui"]["fonts"]["subtitle"],
            foreground=self.config["gui"]["colors"]["accent"]
        )
        self.result_text.tag_configure(
            "value",
            font=self.config["gui"]["fonts"]["content"],
            foreground=self.config["gui"]["colors"]["text"]
        )

    def display_result(self, result):
        """显示分析结果"""
        self.result_text.delete(1.0, END)
        
        # 标题
        self.result_text.insert(END, "手机号码分析报告\n\n", "title")
        
        # 基础信息
        self.result_text.insert(END, "基础信息\n", "section")
        basic_info = result["basic_info"]
        carrier = basic_info["carrier"]
        self.result_text.insert(END, f"号码: {basic_info['number']}\n", "value")
        self.result_text.insert(END, f"运营商: {carrier['operator']}\n", "value")
        self.result_text.insert(END, f"归属地: {carrier['region']}\n", "value")
        self.result_text.insert(END, f"网络类型: {carrier['network']}\n\n", "value")
        
        # 模式分析
        self.result_text.insert(END, "号码模式分析\n", "section")
        pattern_analysis = result["pattern_analysis"]
        if pattern_analysis["special_sequences"]:
            self.result_text.insert(END, "特殊序列:\n", "value")
            for seq in pattern_analysis["special_sequences"]:
                self.result_text.insert(END, f"- {seq['name']}: {seq['description']}\n", "value")
        if pattern_analysis["lucky_numbers"]:
            self.result_text.insert(END, "吉利数字组合:\n", "value")
            for num in pattern_analysis["lucky_numbers"]:
                self.result_text.insert(END, f"- {num}\n", "value")
        self.result_text.insert(END, f"模式评分: {pattern_analysis['pattern_score']}\n\n", "value")
        
        # 数理分析
        self.result_text.insert(END, "数理分析\n", "section")
        numerology = result["numerology"]
        self.result_text.insert(END, f"生命数字: {numerology['life_number']}\n", "value")
        info = numerology["info"]
        self.result_text.insert(END, f"数字性质: {info['nature']}\n", "value")
        self.result_text.insert(END, f"性格特征: {info['characteristics']}\n", "value")
        self.result_text.insert(END, f"事业建议: {info['career']}\n", "value")
        self.result_text.insert(END, f"人际关系: {info['relationships']}\n", "value")
        self.result_text.insert(END, f"吉利元素: {', '.join(info['lucky_elements'])}\n", "value")
        self.result_text.insert(END, f"吉利颜色: {', '.join(info['lucky_colors'])}\n\n", "value")
        
        # 市场价值评估
        self.result_text.insert(END, "市场价值评估\n", "section")
        market_value = result["market_value"]
        self.result_text.insert(END, f"基础分数: {market_value['base_score']}\n", "value")
        self.result_text.insert(END, f"模式质量: {market_value['pattern_quality']:.2f}\n", "value")
        self.result_text.insert(END, f"数理评分: {market_value['numerology_score']:.2f}\n", "value")
        self.result_text.insert(END, f"运营商溢价: {market_value['carrier_premium']:.2f}\n", "value")
        self.result_text.insert(END, f"市场趋势: {market_value['market_trend']:.2f}\n", "value")
        self.result_text.insert(END, f"最终评估分数: {market_value['final_value']}\n", "value")
    
    def evaluate_number(self):
        try:
            # 获取输入的手机号码
            phone_number = self.phone_entry.get().strip()
            
            # 验证手机号码格式
            if not self.validate_phone_number(phone_number):
                raise ValueError("请输入有效的11位手机号码")
            
            # 加载分析数据
            with open(self.analysis_data_path, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            
            # 进行分析
            result = self.analyze_phone_number(phone_number, analysis_data)
            
            # 显示结果
            self.display_result(result)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))
            logging.error(f"分析错误: {str(e)}")
    
    def validate_phone_number(self, phone_number):
        """验证手机号码格式"""
        if not phone_number.isdigit() or len(phone_number) != 11:
            return False
        return True
    
    def analyze_phone_number(self, phone_number, analysis_data):
        """分析手机号码"""
        result = {}
        
        # 基础信息
        result["basic_info"] = {
            "number": phone_number,
            "carrier": self.get_carrier_info(phone_number[:3], analysis_data)
        }
        
        # 数字模式分析
        result["pattern_analysis"] = self.analyze_patterns(phone_number, analysis_data)
        
        # 数理分析
        result["numerology"] = self.analyze_numerology(phone_number, analysis_data)
        
        # 市场价值评估
        result["market_value"] = self.evaluate_market_value(phone_number, result, analysis_data)
        
        return result
    
    def get_carrier_info(self, prefix, analysis_data):
        """获取运营商信息"""
        carrier_info = analysis_data["carrier_info"].get(prefix, {
            "operator": "未知",
            "region": "未知",
            "network": "未知"
        })
        return carrier_info
    
    def analyze_patterns(self, phone_number, analysis_data):
        """分析号码模式"""
        patterns = analysis_data["patterns"]
        result = {
            "special_sequences": [],
            "lucky_numbers": [],
            "pattern_score": 0
        }
        
        # 检查特殊序列
        last_8_digits = phone_number[3:]
        for pattern_name, pattern_info in patterns["special_sequences"].items():
            if self.check_pattern(last_8_digits, pattern_name):
                result["special_sequences"].append({
                    "name": pattern_name,
                    "description": pattern_info["description"],
                    "score": pattern_info["score"]
                })
                result["pattern_score"] += pattern_info["score"]
        
        # 检查吉利数字组合
        for lucky_combo in patterns["lucky_combinations"]:
            if lucky_combo in last_8_digits:
                result["lucky_numbers"].append(lucky_combo)
                result["pattern_score"] += analysis_data["scoring"]["pattern_bonuses"]["lucky_combination"]
        
        return result
    
    def check_pattern(self, number, pattern_type):
        """检查号码是否符合特定模式"""
        if pattern_type == "AAAA":
            return bool(re.search(r"(\d)\1{3}", number))
        elif pattern_type == "ABCD":
            return bool(re.search(r"(?:0123|1234|2345|3456|7890)", number))
        elif pattern_type == "AABB":
            return bool(re.search(r"(\d)\1(\d)\2", number))
        elif pattern_type == "ABAB":
            return bool(re.search(r"(\d)(\d)\1\2", number))
        return False
    
    def analyze_numerology(self, phone_number, analysis_data):
        """数理分析"""
        numerology_info = analysis_data["numerology_info"]
        
        # 计算生命数字（将所有数字相加直到得到一个个位数）
        life_number = sum(int(digit) for digit in phone_number)
        while life_number > 9:
            life_number = sum(int(digit) for digit in str(life_number))
        
        # 获取数理信息
        number_info = numerology_info.get(str(life_number), {
            "nature": "未知",
            "characteristics": "未知",
            "career": "未知",
            "relationships": "未知",
            "lucky_elements": [],
            "lucky_colors": []
        })
        
        return {
            "life_number": life_number,
            "info": number_info
        }
    
    def evaluate_market_value(self, phone_number, analysis_result, analysis_data):
        """评估市场价值"""
        value_factors = analysis_data["value_factors"]
        market_trends = analysis_data["market_trends"]
        
        # 基础分数
        base_score = analysis_data["scoring"]["base_score"]
        
        # 模式质量评分
        pattern_quality = analysis_result["pattern_analysis"]["pattern_score"] / 100.0
        
        # 数理评分
        numerology_score = 0.5 + (analysis_result["numerology"]["life_number"] % 3) * 0.1
        
        # 运营商溢价
        carrier_premium = 1.0
        if analysis_result["basic_info"]["carrier"]["operator"] in ["中国移动", "中国联通", "中国电信"]:
            carrier_premium = 1.2
        
        # 市场趋势
        market_trend = market_trends.get("standard", 1.0)
        for pattern in analysis_result["pattern_analysis"]["special_sequences"]:
            if pattern["name"] in market_trends:
                market_trend = max(market_trend, market_trends[pattern["name"]])
        
        # 计算最终价值
        final_value = base_score * (
            pattern_quality * value_factors["pattern_quality"] +
            numerology_score * value_factors["numerology_score"] +
            carrier_premium * value_factors["carrier_premium"] +
            market_trend * value_factors["market_trend"]
        )
        
        return {
            "base_score": base_score,
            "pattern_quality": pattern_quality,
            "numerology_score": numerology_score,
            "carrier_premium": carrier_premium,
            "market_trend": market_trend,
            "final_value": round(final_value, 2)
        }
    
    def call_ollama_api(self, phone_number):
        try:
            ip_address = self.ip_address_var.get()
            port = self.port_var.get()
            model = self.model_var.get()
            
            logging.info(f"Calling Ollama API with model {model}")
            
            url = f"http://{ip_address}:{port}/v1/completions"
            payload = {"model": model, "prompt": f"Analyze the phone number {phone_number}"}
            
            response = requests.post(url, json=payload, timeout=10)
            response.raise_for_status()
            
            ollama_response = response.json()
            formatted_response = ollama_response.get('choices', [{}])[0].get('text', response.text)
            
            self.result_text.insert(END, f"\nOllama API Response:\n{formatted_response}")
            
        except requests.RequestException as e:
            logging.error(f"Ollama API error: {e}")
            messagebox.showerror("API错误", f"无法连接到Ollama API: {e}")


def main():
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 加载配置文件
        config_path = os.path.join(current_dir, 'config.json')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 设置日志
        logging.basicConfig(
            filename=os.path.join(current_dir, 'phone_analyzer.log'),
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # 创建主窗口
        root = ttk.Window(
            title="手机号码分析专家系统",
            themename=config["gui"]["theme"],
            size=tuple(map(int, config["gui"]["window_size"].split('x')))
        )
        
        # 创建应用实例
        app = PhoneAnalyzerGUI(root, config)
        
        # 运行应用
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")
        logging.error(f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
