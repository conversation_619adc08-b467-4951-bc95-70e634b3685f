document.addEventListener('DOMContentLoaded', function() {
  // 初始化标签页切换
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabs = document.querySelectorAll('.tab');
  
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabId = button.getAttribute('data-tab');
      
      // 切换标签按钮状态
      tabButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');
      
      // 切换标签内容
      tabs.forEach(tab => tab.classList.remove('active'));
      document.getElementById(tabId).classList.add('active');
    });
  });
  
  // 加载保存的联系人数据
  loadContacts();
  
  // 查询功能
  document.getElementById('search-button').addEventListener('click', searchContact);
  document.getElementById('search-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchContact();
    }
  });
  
  // 添加联系人功能
  document.getElementById('add-button').addEventListener('click', addContact);
  
  // 导入/导出功能
  document.getElementById('import-button').addEventListener('click', importContacts);
  document.getElementById('export-button').addEventListener('click', exportContacts);
});

// 存储联系人数据
function saveContacts(contacts) {
  chrome.storage.sync.set({ 'phoneContacts': contacts }, function() {
    console.log('Contacts saved');
  });
}

// 加载联系人数据
function loadContacts() {
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    updateContactsList(contacts);
  });
}

// 更新联系人列表
function updateContactsList(contacts) {
  const contactsList = document.getElementById('contacts-list');
  contactsList.innerHTML = '';
  
  for (const [phoneNumber, name] of Object.entries(contacts)) {
    const row = document.createElement('tr');
    
    const numberCell = document.createElement('td');
    numberCell.textContent = phoneNumber;
    
    const nameCell = document.createElement('td');
    nameCell.textContent = name;
    
    const actionCell = document.createElement('td');
    const deleteButton = document.createElement('button');
    deleteButton.textContent = '删除';
    deleteButton.addEventListener('click', function() {
      deleteContact(phoneNumber);
    });
    actionCell.appendChild(deleteButton);
    
    row.appendChild(numberCell);
    row.appendChild(nameCell);
    row.appendChild(actionCell);
    
    contactsList.appendChild(row);
  }
}

// 搜索联系人
function searchContact() {
  const phoneNumber = document.getElementById('search-input').value.trim();
  if (!phoneNumber) return;
  
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    const resultElement = document.getElementById('search-result');
    const numberElement = document.getElementById('result-number');
    const nameElement = document.getElementById('result-name');
    
    numberElement.textContent = phoneNumber;
    
    if (contacts[phoneNumber]) {
      nameElement.textContent = contacts[phoneNumber];
    } else {
      nameElement.textContent = '未找到匹配的联系人';
    }
    
    resultElement.classList.remove('hidden');
  });
}

// 添加联系人
function addContact() {
  const phoneNumber = document.getElementById('add-number').value.trim();
  const name = document.getElementById('add-name').value.trim();
  const statusElement = document.getElementById('add-status');
  
  if (!phoneNumber || !name) {
    statusElement.textContent = '电话号码和姓名不能为空';
    return;
  }
  
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    contacts[phoneNumber] = name;
    
    saveContacts(contacts);
    updateContactsList(contacts);
    
    // 清空输入框并显示状态
    document.getElementById('add-number').value = '';
    document.getElementById('add-name').value = '';
    statusElement.textContent = '添加成功！';
    
    // 3秒后清除状态信息
    setTimeout(() => {
      statusElement.textContent = '';
    }, 3000);
  });
}

// 删除联系人
function deleteContact(phoneNumber) {
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    
    if (contacts[phoneNumber]) {
      delete contacts[phoneNumber];
      saveContacts(contacts);
      updateContactsList(contacts);
    }
  });
}

// 导入联系人
function importContacts() {
  const importData = document.getElementById('import-data').value.trim();
  if (!importData) return;
  
  const lines = importData.split('\n');
  const newContacts = {};
  
  lines.forEach(line => {
    const [phoneNumber, name] = line.split(',').map(item => item.trim());
    if (phoneNumber && name) {
      newContacts[phoneNumber] = name;
    }
  });
  
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    
    // 合并新旧联系人
    const updatedContacts = { ...contacts, ...newContacts };
    
    saveContacts(updatedContacts);
    updateContactsList(updatedContacts);
    
    // 清空导入文本框
    document.getElementById('import-data').value = '';
    alert('导入成功！');
  });
}

// 导出联系人
function exportContacts() {
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    let exportText = '';
    
    for (const [phoneNumber, name] of Object.entries(contacts)) {
      exportText += `${phoneNumber},${name}\n`;
    }
    
    document.getElementById('export-data').value = exportText;
  });
}