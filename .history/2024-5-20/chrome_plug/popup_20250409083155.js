document.addEventListener('DOMContentLoaded', function() {
  // 初始化标签页切换
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabs = document.querySelectorAll('.tab');
  
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabId = button.getAttribute('data-tab');
      
      // 切换标签按钮状态
      tabButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');
      
      // 切换标签内容
      tabs.forEach(tab => tab.classList.remove('active'));
      document.getElementById(tabId).classList.add('active');
    });
  });

  // 添加自定义字段按钮
  document.getElementById('add-field-button').addEventListener('click', addCustomField);
  
  // 设置按钮事件
  document.getElementById('settings-btn').addEventListener('click', function() {
    tabButtons.forEach(btn => btn.classList.remove('active'));
    tabs.forEach(tab => tab.classList.remove('active'));
    document.getElementById('settings').classList.add('active');
  });
  
  // 加载保存的联系人数据
  loadContacts();
  
  // 加载设置
  loadSettings();
  
  // 查询功能
  document.getElementById('search-button').addEventListener('click', searchContact);
  document.getElementById('search-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchContact();
    }
  });
  
  // 添加联系人功能
  document.getElementById('add-button').addEventListener('click', addContact);
  
  // 导入/导出功能
  document.getElementById('import-button').addEventListener('click', importContacts);
  document.getElementById('import-clear-button').addEventListener('click', clearImportText);
  document.getElementById('export-button').addEventListener('click', exportContacts);
  document.getElementById('export-copy-button').addEventListener('click', copyToClipboard);
  document.getElementById('backup-button').addEventListener('click', backupData);
  document.getElementById('restore-button').addEventListener('click', restoreData);

  // 列表页面功能
  document.getElementById('list-filter').addEventListener('input', filterContacts);
  document.getElementById('select-all-checkbox').addEventListener('change', toggleSelectAll);
  document.getElementById('select-all-button').addEventListener('click', selectAllContacts);
  document.getElementById('delete-selected-button').addEventListener('click', deleteSelectedContacts);

  // 设置保存
  document.getElementById('save-settings-button').addEventListener('click', saveSettings);
  
  // 添加快捷键
  document.addEventListener('keydown', handleKeyboardShortcuts);

  // 添加深色模式切换功能
  initDarkMode();

  // 设置拖放导入功能
  setupDragAndDrop();

  // 用户定义的自定义字段
  let customFields = [];

  // 加载自定义字段列表
  function loadCustomFields(callback) {
    chrome.storage.sync.get('customFields', function(data) {
      customFields = data.customFields || [];
      if (typeof callback === 'function') {
        callback();
      }
    });
  }

  // 保存自定义字段列表
  function saveCustomFields(fields, callback) {
    chrome.storage.sync.set({ 'customFields': fields }, function() {
      customFields = fields;
      if (typeof callback === 'function') {
        callback();
      }
    });
  }

  // 存储联系人数据
  function saveContacts(contacts) {
    chrome.storage.sync.set({ 'phoneContacts': contacts }, function() {
      console.log('Contacts saved');
    });
  }

  // 加载联系人数据
  function loadContacts() {
    loadCustomFields(function() {
      chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
        const contacts = data.phoneContacts || {};
        const starred = data.starredContacts || [];
        
        // 检查并转换旧数据格式
        let needsUpdate = false;
        for (const [phoneNumber, contactData] of Object.entries(contacts)) {
          if (typeof contactData === 'string') {
            contacts[phoneNumber] = {
              name: contactData,
              customData: {}
            };
            needsUpdate = true;
          }
        }
        
        if (needsUpdate) {
          saveContacts(contacts);
        }
        
        updateContactsList(contacts, starred);
      });
    });
  }

  // 更新联系人列表
  function updateContactsList(contacts, starred = []) {
    const contactsList = document.getElementById('contacts-list');
    const filterText = document.getElementById('list-filter').value.toLowerCase();
    contactsList.innerHTML = '';
    
    // 更新表头以包含自定义字段
    updateTableHeader();
    
    // 转换为数组以便排序
    const contactsArray = Object.entries(contacts).map(([phoneNumber, contactData]) => {
      // 如果contactData是字符串，转换为对象格式
      const contact = typeof contactData === 'string' 
        ? { name: contactData, customData: {} } 
        : contactData;
      
      return {
        phoneNumber,
        name: typeof contactData === 'string' ? contactData : contactData.name,
        customData: typeof contactData === 'string' ? {} : (contactData.customData || {}),
        isStarred: starred.includes(phoneNumber)
      };
    });
    
    // 优先排序星标联系人
    contactsArray.sort((a, b) => {
      if (a.isStarred && !b.isStarred) return -1;
      if (!a.isStarred && b.isStarred) return 1;
      return a.name.localeCompare(b.name);
    });
    
    contactsArray.forEach(contact => {
      const { phoneNumber, name, customData, isStarred } = contact;
      
      // 过滤联系人
      if (filterText && !phoneNumber.includes(filterText) && !name.toLowerCase().includes(filterText)) {
        // 检查自定义字段是否匹配
        let matchesCustomField = false;
        for (const field of customFields) {
          if (customData[field] && customData[field].toLowerCase().includes(filterText)) {
            matchesCustomField = true;
            break;
          }
        }
        
        if (!matchesCustomField) {
          return;
        }
      }
      
      const row = document.createElement('tr');
      if (isStarred) {
        row.classList.add('featured');
      }
      
      // 复选框列
      const checkCell = document.createElement('td');
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.className = 'contact-checkbox';
      checkbox.setAttribute('data-number', phoneNumber);
      checkCell.appendChild(checkbox);
      
      // 号码列
      const numberCell = document.createElement('td');
      numberCell.textContent = phoneNumber;
      
      // 姓名列
      const nameCell = document.createElement('td');
      nameCell.textContent = name;
      
      // 自定义字段列
      const customCells = [];
      for (const field of customFields) {
        const customCell = document.createElement('td');
        customCell.textContent = customData[field] || '';
        customCells.push(customCell);
      }
      
      // 操作列
      const actionCell = document.createElement('td');
      const actionDiv = document.createElement('div');
      actionDiv.className = 'action-buttons';
      
      // 星标按钮
      const starButton = document.createElement('button');
      starButton.className = 'btn-star';
      starButton.innerHTML = isStarred ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
      starButton.title = isStarred ? '取消星标' : '设为星标';
      starButton.addEventListener('click', function() {
        toggleStarContact(phoneNumber);
      });
      
      // 编辑按钮
      const editButton = document.createElement('button');
      editButton.className = 'btn-edit';
      editButton.innerHTML = '<i class="fas fa-edit"></i>';
      editButton.title = '编辑';
      editButton.addEventListener('click', function() {
        editContact(phoneNumber, contact);
      });
      
      // 删除按钮
      const deleteButton = document.createElement('button');
      deleteButton.className = 'btn-delete';
      deleteButton.innerHTML = '<i class="fas fa-trash-alt"></i>';
      deleteButton.title = '删除';
      deleteButton.addEventListener('click', function() {
        deleteContact(phoneNumber);
      });
      
      actionDiv.appendChild(starButton);
      actionDiv.appendChild(editButton);
      actionDiv.appendChild(deleteButton);
      actionCell.appendChild(actionDiv);
      
      row.appendChild(checkCell);
      row.appendChild(numberCell);
      row.appendChild(nameCell);
      
      // 添加自定义字段单元格
      customCells.forEach(cell => {
        row.appendChild(cell);
      });
      
      row.appendChild(actionCell);
      
      contactsList.appendChild(row);
    });
  }

  // 更新表头
  function updateTableHeader() {
    const tableHeader = document.querySelector('#contacts-table thead tr');
    
    // 清除现有的表头，除了第一列（复选框）
    while (tableHeader.children.length > 3) {
      tableHeader.removeChild(tableHeader.children[3]);
    }
    
    // 添加自定义字段表头
    for (const field of customFields) {
      const th = document.createElement('th');
      th.textContent = field;
      tableHeader.insertBefore(th, tableHeader.lastElementChild);
    }
  }

  // 编辑联系人信息
  function editContact(phoneNumber, contact) {
    showEditPopup(phoneNumber, contact);
  }

  // 显示编辑联系人弹窗
  function showEditPopup(phoneNumber, contact) {
    // 创建弹窗
    const popup = document.createElement('div');
    popup.className = 'edit-popup';
    
    // 创建基本表单字段
    let formContent = `
      <h3>编辑联系人</h3>
      <div class="edit-form">
        <div class="form-group">
          <label>电话号码</label>
          <input type="text" id="edit-number" value="${phoneNumber}" readonly>
        </div>
        <div class="form-group">
          <label>姓名</label>
          <input type="text" id="edit-name" value="${contact.name}">
        </div>
    `;
    
    // 添加自定义字段输入框
    customFields.forEach(field => {
      const value = contact.customData[field] || '';
      formContent += `
        <div class="form-group">
          <label>${field}</label>
          <input type="text" id="edit-${field}" value="${value}" data-field="${field}">
        </div>
      `;
    });
    
    // 添加按钮
    formContent += `
      </div>
      <div class="button-group">
        <button id="edit-save-btn" class="btn-save">保存</button>
        <button id="edit-cancel-btn" class="btn-cancel">取消</button>
      </div>
    `;
    
    popup.innerHTML = formContent;
    document.body.appendChild(popup);
    
    // 设置按钮事件
    document.getElementById('edit-cancel-btn').addEventListener('click', function() {
      document.body.removeChild(popup);
    });
    
    document.getElementById('edit-save-btn').addEventListener('click', function() {
      const name = document.getElementById('edit-name').value.trim();
      if (!name) {
        showNotification('姓名不能为空', 'error');
        return;
      }
      
      // 获取自定义字段值
      const customData = {};
      customFields.forEach(field => {
        const input = document.querySelector(`#edit-${field}`);
        if (input) {
          customData[field] = input.value.trim();
        }
      });
      
      // 更新联系人信息
      saveContact(phoneNumber, name, customData);
      document.body.removeChild(popup);
    });
  }

  // 保存联系人信息
  function saveContact(phoneNumber, name, customData) {
    chrome.storage.sync.get('phoneContacts', function(data) {
      const contacts = data.phoneContacts || {};
      
      // 更新联系人数据为对象格式
      contacts[phoneNumber] = {
        name: name,
        customData: customData
      };
      
      saveContacts(contacts);
      loadContacts();
      showNotification('联系人已更新', 'success');
    });
  }

  // 模糊搜索功能
  function fuzzySearch(text, query) {
    if (!text || !query) return false;
    
    // 处理各种格式的电话号码
    if (/^\d+$/.test(query)) {
      // 如果搜索词是纯数字，尝试匹配电话号码中的任何部分
      return text.replace(/\D/g, '').includes(query);
    }
    
    // 对中文和英文进行不同的处理
    const chinesePattern = /[\u4e00-\u9fa5]/;
    if (chinesePattern.test(query)) {
      // 中文搜索：匹配任何包含这些字符的字符串
      return query.split('').every(char => text.includes(char));
    } else {
      // 英文搜索：分词后匹配
      const queryWords = query.toLowerCase().split(/\s+/);
      const textLower = text.toLowerCase();
      return queryWords.every(word => textLower.includes(word));
    }
  }

  // 搜索联系人
  function searchContact() {
    const searchText = document.getElementById('search-input').value.trim();
    if (!searchText) return;
    
    chrome.storage.sync.get('phoneContacts', function(data) {
      const contacts = data.phoneContacts || {};
      const resultElement = document.getElementById('search-result');
      const numberElement = document.getElementById('result-number');
      const nameElement = document.getElementById('result-name');
      
      // 直接搜索电话号码
      if (contacts[searchText]) {
        const contactData = contacts[searchText];
        const name = typeof contactData === 'string' ? contactData : contactData.name;
        
        numberElement.textContent = searchText;
        nameElement.textContent = name;
        resultElement.style.backgroundColor = 'var(--success-color)';
        resultElement.style.color = 'white';
        resultElement.classList.remove('hidden');
        
        // 添加快捷操作按钮
        addQuickActions(resultElement, searchText, name);
        return;
      }
      
      // 搜索名称和模糊搜索
      let matches = [];
      for (const [phoneNumber, contactData] of Object.entries(contacts)) {
        const name = typeof contactData === 'string' ? contactData : contactData.name;
        const customData = typeof contactData === 'string' ? {} : (contactData.customData || {});
        
        // 搜索电话号码（模糊匹配）
        if (fuzzySearch(phoneNumber, searchText)) {
          matches.push({ phoneNumber, name, customData });
          continue;
        }
        
        // 搜索姓名（模糊匹配）
        if (fuzzySearch(name, searchText)) {
          matches.push({ phoneNumber, name, customData });
          continue;
        }
        
        // 搜索自定义字段
        let foundInCustomData = false;
        for (const [field, value] of Object.entries(customData)) {
          if (fuzzySearch(value, searchText)) {
            matches.push({ phoneNumber, name, customData });
            foundInCustomData = true;
            break;
          }
        }
        if (foundInCustomData) continue;
      }
      
      // 根据匹配结果数量显示不同的信息
      if (matches.length === 1) {
        // 只有一个匹配项
        numberElement.textContent = matches[0].phoneNumber;
        nameElement.textContent = matches[0].name;
        resultElement.style.backgroundColor = 'var(--success-color)';
        resultElement.style.color = 'white';
        
        // 添加快捷操作按钮
        addQuickActions(resultElement, matches[0].phoneNumber, matches[0].name);
      } else if (matches.length > 1) {
        // 多个匹配项，显示第一个并提示有多个
        numberElement.textContent = matches[0].phoneNumber;
        nameElement.textContent = `${matches[0].name} (找到 ${matches.length} 个匹配联系人)`;
        resultElement.style.backgroundColor = 'var(--warning-color)';
        resultElement.style.color = 'white';
        
        // 添加快捷操作按钮
        addQuickActions(resultElement, matches[0].phoneNumber, matches[0].name);
        
        // 过滤列表页面显示所有匹配项
        document.getElementById('list-filter').value = searchText;
        
        // 显示提示消息
        showNotification(`找到 ${matches.length} 个匹配联系人，已在列表中显示`, 'info');
        
        // 切换到列表页面
        setTimeout(() => {
          const listTabButton = document.querySelector('.tab-button[data-tab="list"]');
          if (listTabButton) {
            listTabButton.click();
          }
        }, 1000);
      } else {
        // 没有匹配项
        numberElement.textContent = searchText;
        nameElement.textContent = '未找到匹配的联系人';
        resultElement.style.backgroundColor = 'var(--secondary-color)';
        resultElement.style.color = 'var(--text-color)';
      }
      
      resultElement.classList.remove('hidden');
    });
  }

  // 添加快捷操作按钮
  function addQuickActions(resultElement, phoneNumber, name) {
    // 移除现有的快捷操作按钮
    const existingActions = resultElement.querySelector('.quick-actions');
    if (existingActions) {
      existingActions.remove();
    }
    
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'quick-actions';
    
    // 复制按钮
    const copyBtn = document.createElement('button');
    copyBtn.className = 'action-button';
    copyBtn.innerHTML = '📋 复制号码';
    copyBtn.addEventListener('click', function() {
      navigator.clipboard.writeText(phoneNumber).then(function() {
        showNotification('号码已复制到剪贴板', 'success');
      });
    });
    
    // 编辑按钮
    const editBtn = document.createElement('button');
    editBtn.className = 'action-button';
    editBtn.innerHTML = '✏️ 编辑';
    editBtn.addEventListener('click', function() {
      chrome.storage.sync.get('phoneContacts', function(data) {
        const contacts = data.phoneContacts || {};
        if (contacts[phoneNumber]) {
          editContact(phoneNumber, contacts[phoneNumber]);
        }
      });
    });
    
    // 拨号按钮
    const callBtn = document.createElement('button');
    callBtn.className = 'action-button';
    callBtn.innerHTML = '📞 拨号';
    callBtn.addEventListener('click', function() {
      window.open(`tel:${phoneNumber}`);
    });
    
    actionsDiv.appendChild(copyBtn);
    actionsDiv.appendChild(editBtn);
    actionsDiv.appendChild(callBtn);
    resultElement.appendChild(actionsDiv);
  }

  // 添加联系人
  function addContact() {
    const phoneNumber = document.getElementById('add-number').value.trim();
    const name = document.getElementById('add-name').value.trim();
    
    if (!phoneNumber || !name) {
      showNotification('电话号码和姓名不能为空', 'error');
      return;
    }
    
    chrome.storage.sync.get('phoneContacts', function(data) {
      const contacts = data.phoneContacts || {};
      const isNew = !contacts[phoneNumber];
      
      // 保存为对象格式
      contacts[phoneNumber] = {
        name: name,
        customData: {}
      };
      
      saveContacts(contacts);
      loadContacts();
      
      // 清空输入框
      document.getElementById('add-number').value = '';
      document.getElementById('add-name').value = '';
      
      // 显示成功通知
      showNotification(
        isNew ? '联系人添加成功！' : '联系人更新成功！', 
        'success'
      );
    });
  }

  // 删除联系人
  function deleteContact(phoneNumber) {
    if (confirm('确定要删除这个联系人吗？')) {
      chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
        const contacts = data.phoneContacts || {};
        const starred = data.starredContacts || [];
        
        if (contacts[phoneNumber]) {
          delete contacts[phoneNumber];
          saveContacts(contacts);
          
          // 如果是星标联系人，也要从星标列表中移除
          const starredIndex = starred.indexOf(phoneNumber);
          if (starredIndex !== -1) {
            starred.splice(starredIndex, 1);
            chrome.storage.sync.set({ 'starredContacts': starred });
          }
          
          loadContacts();
          showNotification('联系人已删除', 'success');
        }
      });
    }
  }

  // 导入联系人
  function importContacts() {
    const importData = document.getElementById('import-data').value.trim();
    if (!importData) {
      showNotification('请输入要导入的数据', 'error');
      return;
    }
    
    // 验证导入数据
    const errors = validateImportData(importData);
    if (errors.length > 0) {
      // 显示验证错误信息
      showNotification(`导入数据格式错误，第一个错误：${errors[0]}`, 'error');
      return;
    }
    
    const lines = importData.split('\n');
    const newContacts = {};
    let validCount = 0;
    
    lines.forEach(line => {
      const parts = line.split(',').map(item => item.trim());
      if (parts.length >= 2) {
        const phoneNumber = parts[0];
        const name = parts[1];
        const customData = {};
        
        // 处理可能的自定义字段
        for (let i = 0; i < customFields.length && i + 2 < parts.length; i++) {
          customData[customFields[i]] = parts[i + 2];
        }
        
        if (phoneNumber && name) {
          newContacts[phoneNumber] = {
            name: name,
            customData: customData
          };
          validCount++;
        }
      }
    });
    
    if (validCount === 0) {
      showNotification('没有有效的联系人数据', 'error');
      return;
    }
    
    // 检查是否有重复的联系人
    chrome.storage.sync.get('phoneContacts', function(data) {
      const existingContacts = data.phoneContacts || {};
      let duplicateCount = 0;
      
      for (const phoneNumber in newContacts) {
        if (existingContacts[phoneNumber]) {
          duplicateCount++;
        }
      }
      
      // 显示导入确认对话框
      const confirmMessage = duplicateCount > 0 
        ? `即将导入 ${validCount} 个联系人，其中 ${duplicateCount} 个将覆盖现有数据。确定继续吗？`
        : `即将导入 ${validCount} 个联系人。确定继续吗？`;
      
      if (confirm(confirmMessage)) {
        // 合并联系人数据
        for (const [phoneNumber, contactData] of Object.entries(newContacts)) {
          existingContacts[phoneNumber] = contactData;
        }
        
        chrome.storage.sync.set({ 'phoneContacts': existingContacts }, function() {
          loadContacts();
          showNotification(`成功导入 ${validCount} 个联系人！`, 'success');
          
          // 清空导入文本框
          document.getElementById('import-data').value = '';
        });
      }
    });
  }

  // 清空导入文本框
  function clearImportText() {
    document.getElementById('import-data').value = '';
  }

  // 导出联系人
  function exportContacts() {
    chrome.storage.sync.get('phoneContacts', function(data) {
      const contacts = data.phoneContacts || {};
      let exportText = '';
      
      // 创建表头
      exportText = `电话号码,姓名${customFields.length ? ',' + customFields.join(',') : ''}\n`;
      
      for (const [phoneNumber, contactData] of Object.entries(contacts)) {
        const name = typeof contactData === 'string' ? contactData : contactData.name;
        let line = `${phoneNumber},${name}`;
        
        // 添加自定义字段值
        if (typeof contactData !== 'string' && contactData.customData) {
          for (const field of customFields) {
            line += `,${contactData.customData[field] || ''}`;
          }
        } else {
          // 为旧数据格式添加空自定义字段
          line += ','.repeat(customFields.length);
        }
        
        exportText += line + '\n';
      }
      
      document.getElementById('export-data').value = exportText || '暂无联系人';
    });
  }

  // 复制导出数据到剪贴板
  function copyToClipboard() {
    const exportText = document.getElementById('export-data');
    exportText.select();
    document.execCommand('copy');
    showNotification('已复制到剪贴板', 'success');
  }

  // 备份数据
  function backupData() {
    chrome.storage.sync.get(['phoneContacts', 'starredContacts', 'settings'], function(data) {
      const backupData = JSON.stringify(data, null, 2);
      const blob = new Blob([backupData], {type: 'application/json'});
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `phone_contacts_backup_${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    });
  }

  // 恢复数据
  function restoreData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = e => {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onload = event => {
        try {
          const data = JSON.parse(event.target.result);
          
          if (data.phoneContacts) {
            chrome.storage.sync.set(data, function() {
              loadContacts();
              loadSettings();
              showNotification('数据恢复成功！', 'success');
            });
          } else {
            showNotification('无效的备份文件', 'error');
          }
        } catch (e) {
          showNotification('无效的JSON格式', 'error');
        }
      };
      
      reader.readAsText(file);
    };
    
    input.click();
  }

  // 过滤联系人列表
  function filterContacts() {
    chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
      const contacts = data.phoneContacts || {};
      const starred = data.starredContacts || [];
      updateContactsList(contacts, starred);
    });
  }

  // 切换选择所有联系人
  function toggleSelectAll() {
    const isChecked = document.getElementById('select-all-checkbox').checked;
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    
    checkboxes.forEach(checkbox => {
      checkbox.checked = isChecked;
    });
  }

  // 选择所有联系人
  function selectAllContacts() {
    document.getElementById('select-all-checkbox').checked = true;
    toggleSelectAll();
  }

  // 删除选中的联系人
  function deleteSelectedContacts() {
    const selected = document.querySelectorAll('.contact-checkbox:checked');
    if (selected.length === 0) {
      showNotification('请先选择要删除的联系人', 'error');
      return;
    }
    
    if (confirm(`确定要删除选中的 ${selected.length} 个联系人吗？`)) {
      chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
        const contacts = data.phoneContacts || {};
        const starred = data.starredContacts || [];
        let updated = false;
        
        selected.forEach(checkbox => {
          const number = checkbox.getAttribute('data-number');
          if (contacts[number]) {
            delete contacts[number];
            updated = true;
            
            // 从星标中删除
            const starIndex = starred.indexOf(number);
            if (starIndex !== -1) {
              starred.splice(starIndex, 1);
            }
          }
        });
        
        if (updated) {
          chrome.storage.sync.set({ 
            'phoneContacts': contacts,
            'starredContacts': starred
          }, function() {
            loadContacts();
            showNotification(`已删除 ${selected.length} 个联系人`, 'success');
          });
        }
      });
    }
  }

  // 切换星标状态
  function toggleStarContact(phoneNumber) {
    chrome.storage.sync.get('starredContacts', function(data) {
      const starred = data.starredContacts || [];
      const index = starred.indexOf(phoneNumber);
      
      if (index === -1) {
        starred.push(phoneNumber);
      } else {
        starred.splice(index, 1);
      }
      
      chrome.storage.sync.set({ 'starredContacts': starred }, function() {
        loadContacts();
      });
    });
  }

  // 显示通知
  function showNotification(message, type) {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    container.appendChild(notification);
    
    // 3秒后自动移除通知
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        container.removeChild(notification);
      }, 300);
    }, 3000);
  }

  // 加载设置
  function loadSettings() {
    chrome.storage.sync.get('settings', function(data) {
      const settings = data.settings || {};
      document.getElementById('autoRecognition').checked = settings.autoRecognition !== false;
      document.getElementById('showTooltips').checked = settings.showTooltips !== false;
      document.getElementById('showNames').checked = settings.showNames !== false;
      document.getElementById('autoFill').checked = settings.autoFill !== false;
      document.getElementById('autoJump').checked = settings.autoJump !== false;
    });
  }

  // 保存设置
  function saveSettings() {
    const settings = {
      autoRecognition: document.getElementById('autoRecognition').checked,
      showTooltips: document.getElementById('showTooltips').checked,
      showNames: document.getElementById('showNames').checked,
      autoFill: document.getElementById('autoFill').checked,
      autoJump: document.getElementById('autoJump').checked
    };
    
    chrome.storage.sync.set({ 'settings': settings }, function() {
      showNotification('设置已保存', 'success');
    });
  }

  // 初始化设置页面
  function initSettingsTab() {
    // 加载设置
    loadSettings();
    
    // 设置事件监听器
    document.getElementById('autoRecognition').addEventListener('change', saveSettings);
    document.getElementById('showTooltips').addEventListener('change', saveSettings);
    document.getElementById('showNames').addEventListener('change', saveSettings);
    document.getElementById('autoFill').addEventListener('change', saveSettings);
    document.getElementById('autoJump').addEventListener('change', saveSettings);
  }

  // 处理键盘快捷键
  function handleKeyboardShortcuts(e) {
    // Ctrl+F 或 Cmd+F (Mac) 聚焦到搜索框
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
      const activeTabId = document.querySelector('.tab.active').id;
      
      if (activeTabId === 'search') {
        document.getElementById('search-input').focus();
        e.preventDefault();
      } else if (activeTabId === 'list') {
        document.getElementById('list-filter').focus();
        e.preventDefault();
      }
    }
    
    // Ctrl+S 或 Cmd+S (Mac) 在添加页面时保存
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
      const activeTabId = document.querySelector('.tab.active').id;
      
      if (activeTabId === 'add') {
        addContact();
        e.preventDefault();
      }
    }
  }

  // 添加自定义字段
  function addCustomField() {
    const fieldName = document.getElementById('new-field-name').value.trim();
    
    if (!fieldName) {
      showNotification('字段名称不能为空', 'error');
      return;
    }
    
    if (customFields.includes(fieldName)) {
      showNotification('字段名称已存在', 'error');
      return;
    }
    
    // 添加新字段
    customFields.push(fieldName);
    saveCustomFields(customFields, function() {
      // 更新字段列表显示
      updateCustomFieldsList();
      // 更新表格
      loadContacts();
      // 清空输入框
      document.getElementById('new-field-name').value = '';
      showNotification('已添加新字段', 'success');
    });
  }

  // 移除自定义字段
  function removeCustomField(fieldName) {
    const index = customFields.indexOf(fieldName);
    if (index !== -1) {
      customFields.splice(index, 1);
      saveCustomFields(customFields, function() {
        // 更新字段列表显示
        updateCustomFieldsList();
        // 更新表格
        loadContacts();
        showNotification('已删除字段', 'success');
      });
    }
  }

  // 更新自定义字段列表UI
  function updateCustomFieldsList() {
    const fieldsList = document.getElementById('custom-fields-list');
    fieldsList.innerHTML = '';
    
    customFields.forEach(field => {
      const item = document.createElement('div');
      item.className = 'field-item';
      
      const fieldText = document.createElement('span');
      fieldText.textContent = field;
      
      const deleteBtn = document.createElement('button');
      deleteBtn.className = 'btn-delete-field';
      deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
      deleteBtn.addEventListener('click', function() {
        removeCustomField(field);
      });
      
      item.appendChild(fieldText);
      item.appendChild(deleteBtn);
      fieldsList.appendChild(item);
    });
  }

  // 初始化深色模式
  function initDarkMode() {
    // 动态添加深色模式样式到head
    const style = document.createElement('style');
    style.textContent = `
      /* 深色模式样式 */
      body.dark-mode {
        background-color: #1a1a1a;
        color: #ecf0f1;
      }
      
      body.dark-mode .app-header {
        background-color: #2c3e50;
      }
      
      body.dark-mode .tab-buttons {
        background-color: #2c3e50;
        border-color: #34495e;
      }
      
      body.dark-mode .tab-button:not(.active) {
        background-color: #34495e;
        color: #ecf0f1;
        border-color: #2c3e50;
      }
      
      body.dark-mode input, 
      body.dark-mode textarea,
      body.dark-mode button:not(.tab-button):not(.settings-btn) {
        background-color: #34495e;
        color: #ecf0f1;
        border-color: #2c3e50;
      }
      
      body.dark-mode th {
        background-color: #2980b9;
      }
      
      body.dark-mode tr:nth-child(even) {
        background-color: #2c3e50;
      }
      
      body.dark-mode tr:hover {
        background-color: #34495e;
      }
      
      body.dark-mode .search-result {
        background-color: #2c3e50;
      }
      
      /* 快捷操作按钮样式 */
      .quick-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        gap: 10px;
      }
      
      .action-button {
        flex: 1;
        padding: 8px 0;
        border: none;
        border-radius: 4px;
        background-color: var(--primary-color);
        color: white;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      
      .action-button:hover {
        background-color: var(--primary-dark);
      }
      
      /* 深色模式开关样式 */
      .theme-switch-wrapper {
        display: flex;
        align-items: center;
        margin: 15px 0;
      }
      
      .theme-switch-wrapper span {
        margin-right: 10px;
      }
      
      .theme-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 30px;
      }
      
      .theme-switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }
      
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
      }
      
      .slider:before {
        position: absolute;
        content: "";
        height: 22px;
        width: 22px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
      }
      
      input:checked + .slider {
        background-color: var(--primary-color);
      }
      
      input:checked + .slider:before {
        transform: translateX(26px);
      }
      
      /* 拖放导入区域 */
      .drag-over {
        background-color: rgba(74, 137, 220, 0.1);
        border: 2px dashed var(--primary-color);
      }
    `;
    document.head.appendChild(style);
    
    // 在设置部分添加深色模式开关
    const settingsSection = document.querySelector('.settings-section');
    if (settingsSection) {
      const darkModeToggle = document.createElement('div');
      darkModeToggle.className = 'setting-item theme-switch-wrapper';
      darkModeToggle.innerHTML = `
        <span>深色模式</span>
        <label class="theme-switch">
          <input type="checkbox" id="darkModeToggle">
          <span class="slider"></span>
        </label>
      `;
      settingsSection.appendChild(darkModeToggle);
      
      // 检查当前模式并设置开关状态
      chrome.storage.sync.get('darkMode', function(data) {
        const isDarkMode = data.darkMode === true;
        const darkModeToggleInput = document.getElementById('darkModeToggle');
        
        if (isDarkMode) {
          document.body.classList.add('dark-mode');
          if (darkModeToggleInput) darkModeToggleInput.checked = true;
        }
        
        // 监听切换事件
        if (darkModeToggleInput) {
          darkModeToggleInput.addEventListener('change', function() {
            if (this.checked) {
              document.body.classList.add('dark-mode');
              chrome.storage.sync.set({ 'darkMode': true });
            } else {
              document.body.classList.remove('dark-mode');
              chrome.storage.sync.set({ 'darkMode': false });
            }
          });
        }
      });
    }
  }

  // 验证导入数据格式
  function validateImportData(data) {
    const lines = data.split('\n');
    const errors = [];
    const phoneRegex = /^1[3-9]\d{9}$|^(0\d{2,3}-?)?[2-9]\d{6,7}$/; // 支持手机和座机格式
    
    lines.forEach((line, index) => {
      if (!line.trim()) return; // 跳过空行
      
      const parts = line.split(',');
      const lineNumber = index + 1;
      
      if (parts.length < 2) {
        errors.push(`第 ${lineNumber} 行: 格式错误，至少需要电话号码和姓名，用逗号分隔`);
      } else {
        const phoneNumber = parts[0].trim();
        const name = parts[1].trim();
        
        if (!phoneNumber) {
          errors.push(`第 ${lineNumber} 行: 电话号码不能为空`);
        } else if (!phoneRegex.test(phoneNumber)) {
          errors.push(`第 ${lineNumber} 行: 电话号码 "${phoneNumber}" 格式不正确`);
        }
        
        if (!name) {
          errors.push(`第 ${lineNumber} 行: 姓名不能为空`);
        }
      }
    });
    
    // 限制错误数量，避免太多错误信息
    if (errors.length > 5) {
      const remainingErrors = errors.length - 5;
      errors.splice(5, remainingErrors, `... 还有 ${remainingErrors} 个错误`);
    }
    
    return errors;
  }

  // 支持拖放导入CSV文件
  function setupDragAndDrop() {
    const importArea = document.getElementById('import-data');
    
    if (importArea) {
      // 防止默认拖放行为
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        importArea.addEventListener(eventName, preventDefaults, false);
      });
      
      function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
      }
      
      // 高亮拖放区域
      ['dragenter', 'dragover'].forEach(eventName => {
        importArea.addEventListener(eventName, highlight, false);
      });
      
      ['dragleave', 'drop'].forEach(eventName => {
        importArea.addEventListener(eventName, unhighlight, false);
      });
      
      function highlight() {
        importArea.classList.add('drag-over');
      }
      
      function unhighlight() {
        importArea.classList.remove('drag-over');
      }
      
      // 处理文件拖放
      importArea.addEventListener('drop', handleDrop, false);
      
      function handleDrop(e) {
        const files = e.dataTransfer.files;
        if (files.length) {
          const file = files[0];
          if (file.type === 'text/csv' || file.name.endsWith('.csv') || file.name.endsWith('.txt')) {
            const reader = new FileReader();
            reader.onload = function(e) {
              importArea.value = e.target.result;
              showNotification('文件已加载，请点击"导入"按钮完成导入', 'success');
            };
            reader.readAsText(file);
          } else {
            showNotification('请拖放CSV文件或文本文件', 'error');
          }
        }
      }
    }
  }
});