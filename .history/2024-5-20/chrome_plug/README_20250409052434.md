<!--
 * @Author: hgxszhj <EMAIL>
 * @Date: 2025-04-09 05:11:07
 * @LastEditors: hgxszhj <EMAIL>
 * @LastEditTime: 2025-04-09 05:23:33
 * @FilePath: /my_project/2024-5-20/chrome_plug/README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 电话号码助手 Chrome 扩展

这是一个功能强大的 Chrome 浏览器扩展，可以将网页上的电话号码与您保存的联系人姓名关联起来，方便识别电话号码的归属。

## 功能特点

- **查询功能**：输入电话号码快速查询对应的联系人姓名
- **添加功能**：手动添加电话号码与联系人姓名的对应关系
- **导入/导出**：批量导入导出电话号码与姓名的对应关系，支持备份和恢复
- **自动识别**：自动识别网页上的电话号码，并显示对应的联系人姓名
- **悬停显示**：鼠标悬停在网页上的电话号码上，会显示对应的联系人姓名
- **右键菜单**：选中电话号码后，可通过右键菜单快速添加或查询
- **快速复制**：点击网页上的电话号码可快速复制到剪贴板
- **星标联系人**：可以将常用联系人标记为星标，方便快速查找
- **批量操作**：支持批量选择和删除联系人
- **快捷键支持**：使用快捷键快速打开扩展和添加联系人
- **丰富设置**：提供多种设置选项，满足个性化需求

## 安装方法

1. 下载本扩展的所有文件
2. 在 Chrome 浏览器中打开扩展管理页面：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含本扩展所有文件的文件夹

## 基本使用

### 查询联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 在弹出的窗口中，确保在"查询"标签下
3. 输入要查询的电话号码
4. 点击"查询"按钮，结果会显示在下方

### 添加联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"添加"标签
3. 输入电话号码和对应的姓名
4. 点击"添加"按钮保存

### 导入联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"导入/导出"标签
3. 在文本框中输入要导入的数据，格式为每行一条记录，电话号码和姓名用逗号分隔，例如：`13800000000,张三`
4. 点击"导入"按钮

### 导出联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"导入/导出"标签
3. 点击"导出所有数据"按钮
4. 数据会显示在下方的文本框中，可以点击"复制到剪贴板"按钮复制数据

## 高级功能

### 右键菜单

在网页上选中一个电话号码，右键点击，可以看到以下选项：
- **添加到电话号码助手**：快速添加选中的电话号码
- **查询联系人**：查询选中的电话号码对应的联系人
- **复制电话号码**：复制格式化后的电话号码

### 联系人列表管理

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"列表"标签
3. 可以在这里查看所有联系人，支持以下操作：
   - 搜索过滤联系人
   - 标记/取消标记为星标
   - 删除单个联系人
   - 全选/批量删除

### 数据备份与恢复

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"导入/导出"标签
3. 点击"备份数据"按钮，将生成一个包含所有数据的JSON文件
4. 点击"恢复数据"按钮，选择之前备份的JSON文件进行恢复

### 设置选项

点击扩展窗口右上角的齿轮图标，可以进入设置页面：
- **启用网页自动识别**：开启或关闭自动识别网页上的电话号码
- **显示悬停提示**：开启或关闭鼠标悬停显示联系人信息
- **在电话号码旁显示姓名**：开启或关闭在电话号码旁显示联系人姓名

### 快捷键

- **Ctrl+Shift+P** (Mac上为 **Command+Shift+P**)：快速打开扩展
- **Alt+Shift+N**：快速添加选中的电话号码
- 在扩展内部：
  - **Ctrl+F** (Mac上为 **Command+F**)：在列表或搜索页面快速聚焦到搜索框
  - **Ctrl+S** (Mac上为 **Command+S**)：在添加页面快速保存联系人

## 注意事项

- 本扩展使用 Chrome 的 storage.sync API 存储数据，数据会自动同步到您登录的所有 Chrome 浏览器
- 导入数据时，如果有重复的电话号码，新的姓名会覆盖原有的姓名
- 星标联系人在列表中会优先显示并有特殊标记
- 扩展会定期扫描页面以识别新加载的内容中的电话号码
- 点击网页上的电话号码可以快速复制到剪贴板

## 图标替换说明

本扩展的图标文件夹中包含的是占位文件，您需要将它们替换为实际的 PNG 图标文件：
- icon16.png - 16x16 像素
- icon48.png - 48x48 像素
- icon128.png - 128x128 像素

## 更新日志

### v1.1
- 新增美化界面，采用现代化设计
- 添加星标联系人功能
- 增加批量选择和删除功能
- 新增右键菜单和快捷键支持
- 添加设置选项，可自定义功能
- 新增数据备份和恢复功能
- 优化电话号码识别和显示效果
- 增加通知提示系统
- 添加点击复制功能

### v1.0
- 基础查询、添加、导入导出功能
- 网页电话号码识别
- 悬停显示联系人信息 