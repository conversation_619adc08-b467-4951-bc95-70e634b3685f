<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电话号码识别插件</title>
  <style>
    :root {
      --primary-color: #5b8fe6;
      --primary-dark: #4a7dd1;
      --secondary-color: #f5f7fa;
      --text-color: #434a54;
      --border-color: #e6e9ed;
    }
    
    body {
      width: 380px;
      padding: 0;
      font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
      color: var(--text-color);
      background-color: #fff;
      margin: 0;
    }
    
    .app-header {
      background-color: var(--primary-color);
      color: white;
      padding: 15px;
      text-align: center;
      position: relative;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .app-title {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
    }
    
    .settings-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 1.2em;
    }
    
    .tab-buttons {
      display: flex;
      border-bottom: 1px solid var(--border-color);
    }
    
    .tab-button {
      flex: 1;
      padding: 12px;
      background-color: #f0f3f8;
      border: none;
      cursor: pointer;
      color: var(--text-color);
      font-size: 16px;
      transition: all 0.3s;
    }
    
    .tab-button.active {
      background-color: white;
      border-bottom: 2px solid var(--primary-color);
      font-weight: bold;
    }
    
    .tab {
      display: none;
      padding: 15px;
    }
    
    .tab.active {
      display: block;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <div class="app-header">
      <h1 class="app-title">电话号码识别</h1>
      <button class="settings-btn">设置</button>
    </div>
    
    <div class="tab-container">
      <div class="tab-buttons">
        <button class="tab-button active" data-tab="contacts-tab">联系人</button>
        <button class="tab-button" data-tab="fields-tab">自定义字段</button>
        <button class="tab-button" data-tab="settings-tab">设置</button>
      </div>
      
      <div class="tab active" id="contacts-tab">
        <div class="contacts-content">
          <!-- 联系人内容 -->
        </div>
      </div>
      
      <div class="tab" id="fields-tab">
        <div class="fields-content">
          <!-- 自定义字段内容 -->
        </div>
      </div>
      
      <div class="tab" id="settings-tab">
        <div class="settings-content">
          <!-- 设置内容 -->
        </div>
      </div>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html> 