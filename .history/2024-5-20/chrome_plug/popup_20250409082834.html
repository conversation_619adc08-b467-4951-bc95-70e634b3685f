<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>电话号码助手</title>
  <!-- 使用本地Font Awesome图标而不是CDN -->
  <style>
    /* Font Awesome 基础样式 */
    .fas, .far {
      display: inline-block;
      font-style: normal;
      font-variant: normal;
      text-rendering: auto;
      line-height: 1;
      font-family: Arial, sans-serif;
      font-weight: 900;
      -moz-osx-font-smoothing: grayscale;
      -webkit-font-smoothing: antialiased;
    }
    
    /* 图标代码 */
    .fa-search:before { content: "🔍"; }
    .fa-user-plus:before { content: "👤+"; }
    .fa-file-import:before { content: "📥"; }
    .fa-address-book:before { content: "📒"; }
    .fa-phone-alt:before { content: "📱"; }
    .fa-user:before { content: "👤"; }
    .fa-phone:before { content: "📞"; }
    .fa-plus-circle:before { content: "➕"; }
    .fa-trash-alt:before { content: "🗑️"; }
    .fa-copy:before { content: "📋"; }
    .fa-download:before { content: "💾"; }
    .fa-save:before { content: "💾"; }
    .fa-upload:before { content: "📤"; }
    .fa-filter:before { content: "🔍"; }
    .fa-check-double:before { content: "✓✓"; }
    .fa-cog:before { content: "⚙️"; }
    .fa-star:before { content: "⭐"; }
    .fa-check-circle:before { content: "✓"; }
    .fa-exclamation-circle:before { content: "❗"; }
    .fa-exclamation-triangle:before { content: "⚠️"; }
    .fa-info-circle:before { content: "ℹ️"; }
    
    :root {
      --primary-color: #4a89dc;
      --primary-dark: #3a6fc6;
      --secondary-color: #f5f7fa;
      --success-color: #37bc9b;
      --danger-color: #e9573f;
      --warning-color: #f6bb42;
      --text-color: #434a54;
      --border-color: #e6e9ed;
    }
    
    body {
      width: 380px;
      padding: 0;
      font-family: 'Segoe UI', Arial, sans-serif;
      color: var(--text-color);
      background-color: #fff;
      margin: 0;
    }
    
    h2 {
      color: var(--primary-color);
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 10px;
      margin-top: 0;
      text-align: center;
    }
    
    .tab {
      display: none;
      animation: fadeIn 0.3s;
      padding: 0 15px 15px 15px;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .active {
      display: block;
    }
    
    .tab-buttons {
      display: flex;
      margin: 0 0 15px 0;
      border-radius: 0;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .tab-button {
      flex: 1;
      padding: 10px;
      background-color: var(--secondary-color);
      border: none;
      border-right: 1px solid var(--border-color);
      cursor: pointer;
      color: var(--text-color);
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .tab-button:last-child {
      border-right: none;
    }
    
    .tab-button i {
      margin-bottom: 5px;
      font-size: 1.2em;
    }
    
    .tab-button.active {
      background-color: var(--primary-color);
      color: white;
    }
    
    .tab-button:hover:not(.active) {
      background-color: #e6e9ed;
    }
    
    input, textarea, button:not(.tab-button):not(.settings-btn) {
      margin: 8px 0;
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      transition: border 0.3s;
    }
    
    input:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.2);
    }
    
    textarea {
      height: 100px;
      resize: vertical;
    }
    
    button:not(.tab-button):not(.settings-btn) {
      background-color: var(--primary-color);
      color: white;
      border: none;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.3s;
    }
    
    button:not(.tab-button):not(.settings-btn):hover {
      background-color: var(--primary-dark);
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    th, td {
      padding: 12px 8px;
      text-align: left;
    }
    
    th {
      background-color: var(--primary-color);
      color: white;
      font-weight: normal;
    }
    
    tr:nth-child(even) {
      background-color: var(--secondary-color);
    }
    
    tr:hover {
      background-color: #e6e9ed;
    }
    
    .search-result {
      margin-top: 15px;
      padding: 15px;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      background-color: var(--secondary-color);
      transition: all 0.3s;
    }
    
    .hidden {
      display: none;
    }
    
    .notification {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      text-align: center;
      animation: fadeOut 3s forwards;
      animation-delay: 2s;
    }
    
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
    
    .success {
      background-color: var(--success-color);
      color: white;
    }
    
    .error {
      background-color: var(--danger-color);
      color: white;
    }
    
    .action-buttons {
      display: flex;
      gap: 5px;
    }
    
    .btn-delete {
      background-color: var(--danger-color);
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .btn-star {
      background-color: var(--warning-color);
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .filter-container {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }
    
    .filter-container input {
      padding: 8px;
      margin: 0;
    }
    
    .filter-container i {
      margin-right: 8px;
      color: var(--primary-color);
    }
    
    .import-options {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    
    .import-options button {
      width: 48%;
    }
    
    .export-options {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    
    .export-options button {
      width: 48%;
    }
    
    .featured {
      background-color: #fff8e1;
    }
    
    .settings-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 1.2em;
      z-index: 10;
    }
    
    /* 适应性强的顶部栏 */
    .app-header {
      background-color: var(--primary-color);
      color: white;
      padding: 15px;
      text-align: center;
      position: relative;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      margin-bottom: 0;
    }
    
    .app-title {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .app-title span.icon {
      margin-right: 10px;
      font-size: 1.2em;
    }
    
    #notification-container {
      padding: 0 15px;
    }
    
    .custom-fields {
      margin-top: 10px;
    }
    
    .add-field-form {
      display: flex;
      margin-bottom: 10px;
    }
    
    .add-field-form input {
      flex: 1;
      padding: 6px 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      margin-right: 5px;
    }
    
    .add-field-form button {
      padding: 6px 10px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .fields-list {
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 5px;
    }
    
    .field-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px;
      border-bottom: 1px solid #eee;
    }
    
    .field-item:last-child {
      border-bottom: none;
    }
    
    .btn-delete-field {
      background: none;
      border: none;
      color: #f44336;
      cursor: pointer;
      font-size: 14px;
    }
    
    .edit-popup {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      padding: 20px;
      width: 300px;
      z-index: 1000;
    }
    
    .edit-form {
      margin-bottom: 15px;
    }
    
    .form-group {
      margin-bottom: 10px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }
    
    .form-group input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .button-group {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
    
    .btn-save {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .btn-cancel {
      background-color: #f1f1f1;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    /* 调整表格样式适应动态列 */
    #contacts-table {
      table-layout: auto;
      min-width: 100%;
    }
    
    #contacts-table th, #contacts-table td {
      padding: 8px 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    #contacts-table th:first-child,
    #contacts-table td:first-child {
      width: 30px;
    }
    
    #contacts-table th:nth-child(2),
    #contacts-table td:nth-child(2) {
      width: 120px;
    }
    
    #contacts-table th:nth-child(3),
    #contacts-table td:nth-child(3) {
      width: 120px;
    }
    
    #contacts-table th:last-child,
    #contacts-table td:last-child {
      width: 100px;
    }
  </style>
</head>
<body>
  <div class="app-header">
    <h1 class="app-title"><span class="icon">📱</span> 电话号码助手</h1>
    <button id="settings-btn" class="settings-btn" title="设置">⚙️</button>
  </div>
  
  <div class="tab-buttons">
    <button class="tab-button active" data-tab="search">
      <i class="fas fa-search"></i>
      <span>查询</span>
    </button>
    <button class="tab-button" data-tab="add">
      <i class="fas fa-user-plus"></i>
      <span>添加</span>
    </button>
    <button class="tab-button" data-tab="import">
      <i class="fas fa-file-import"></i>
      <span>导入/导出</span>
    </button>
    <button class="tab-button" data-tab="list">
      <i class="fas fa-address-book"></i>
      <span>列表</span>
    </button>
  </div>
  
  <div id="notification-container"></div>
  
  <div id="search" class="tab active">
    <div class="filter-container">
      <i class="fas fa-search"></i>
      <input type="text" id="search-input" placeholder="输入电话号码或联系人姓名" autocomplete="off">
    </div>
    <button id="search-button"><i class="fas fa-search"></i> 查询</button>
    <div id="search-result" class="search-result hidden">
      <p><strong><i class="fas fa-phone"></i> 电话号码:</strong> <span id="result-number"></span></p>
      <p><strong><i class="fas fa-user"></i> 姓名:</strong> <span id="result-name"></span></p>
    </div>
  </div>
  
  <div id="add" class="tab">
    <div class="filter-container">
      <i class="fas fa-phone"></i>
      <input type="text" id="add-number" placeholder="电话号码" autocomplete="off">
    </div>
    <div class="filter-container">
      <i class="fas fa-user"></i>
      <input type="text" id="add-name" placeholder="姓名" autocomplete="off">
    </div>
    <button id="add-button"><i class="fas fa-plus-circle"></i> 添加</button>
    <div id="add-status"></div>
  </div>
  
  <div id="import" class="tab">
    <h3><i class="fas fa-file-import"></i> 导入数据</h3>
    <p>格式: 每行一条记录，号码和姓名用逗号分隔</p>
    <textarea id="import-data" placeholder="13800000000,张三&#10;13900000000,李四"></textarea>
    <div class="import-options">
      <button id="import-button"><i class="fas fa-file-import"></i> 导入</button>
      <button id="import-clear-button"><i class="fas fa-trash-alt"></i> 清空</button>
    </div>
    
    <h3><i class="fas fa-file-export"></i> 导出数据</h3>
    <div class="export-options">
      <button id="export-button"><i class="fas fa-download"></i> 导出所有数据</button>
      <button id="export-copy-button"><i class="fas fa-copy"></i> 复制到剪贴板</button>
    </div>
    <textarea id="export-data" readonly></textarea>
    <div class="export-options">
      <button id="backup-button"><i class="fas fa-save"></i> 备份数据</button>
      <button id="restore-button"><i class="fas fa-upload"></i> 恢复数据</button>
    </div>
  </div>
  
  <div id="list" class="tab">
    <div class="list-header">
      <input type="text" id="list-filter" placeholder="输入姓名或号码筛选...">
      <div class="list-actions">
        <button id="select-all-button">全选</button>
        <button id="delete-selected-button">删除所选</button>
      </div>
    </div>
    <div class="contacts-table-container">
      <table id="contacts-table">
        <thead>
          <tr>
            <th><input type="checkbox" id="select-all-checkbox"></th>
            <th>电话号码</th>
            <th>姓名</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody id="contacts-list">
          <!-- 联系人列表将在JS中动态生成 -->
        </tbody>
      </table>
    </div>
  </div>

  <div id="settings" class="tab">
    <div class="settings-section">
      <h3>显示设置</h3>
      <div class="setting-item">
        <label>
          <input type="checkbox" id="autoRecognition"> 自动识别网页中的电话号码
        </label>
      </div>
      <div class="setting-item">
        <label>
          <input type="checkbox" id="showTooltips"> 在电话号码上显示联系人信息
        </label>
      </div>
      <div class="setting-item">
        <label>
          <input type="checkbox" id="showNames"> 在电话号码旁显示联系人姓名
        </label>
      </div>
      <div class="setting-item">
        <label>
          <input type="checkbox" id="autoFill"> 智能检测登录表单并提供自动填充
        </label>
      </div>
      <div class="setting-item">
        <label>
          <input type="checkbox" id="autoJump"> 自动跳转到下一个输入框
        </label>
      </div>
    </div>

    <!-- 添加自定义字段部分 -->
    <div class="settings-section">
      <h3>自定义联系人字段</h3>
      <div class="custom-fields">
        <div class="add-field-form">
          <input type="text" id="new-field-name" placeholder="输入新字段名称">
          <button id="add-field-button">添加</button>
        </div>
        <div id="custom-fields-list" class="fields-list">
          <!-- 自定义字段列表将在JS中动态生成 -->
        </div>
      </div>
    </div>

    <div class="settings-section">
      <h3>数据管理</h3>
      <div class="setting-action">
        <button id="backup-button">备份数据</button>
        <button id="restore-button">恢复数据</button>
      </div>
    </div>

    <div class="button-group">
      <button id="save-settings-button" class="primary-button">保存设置</button>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html> 