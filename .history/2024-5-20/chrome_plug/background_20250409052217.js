// 创建右键菜单
chrome.runtime.onInstalled.addListener(function() {
  // 添加电话号码到联系人的右键菜单项
  chrome.contextMenus.create({
    id: "addPhoneNumber",
    title: "添加到电话号码助手",
    contexts: ["selection"]
  });
  
  // 添加查询电话号码的右键菜单项
  chrome.contextMenus.create({
    id: "searchPhoneNumber",
    title: "查询联系人",
    contexts: ["selection"]
  });
  
  // 复制电话号码的右键菜单项
  chrome.contextMenus.create({
    id: "copyPhoneNumber",
    title: "复制电话号码",
    contexts: ["selection"]
  });
});

// 处理右键菜单点击事件
chrome.contextMenus.onClicked.addListener(function(info, tab) {
  const selection = info.selectionText.trim();
  
  // 提取电话号码
  const phoneRegex = /(\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}|\d{11})/;
  const match = selection.match(phoneRegex);
  
  if (!match) {
    // 如果选中的文本不包含电话号码，显示通知
    chrome.tabs.sendMessage(tab.id, {
      action: "showNotification",
      message: "选中的文本不包含有效的电话号码",
      type: "error"
    });
    return;
  }
  
  // 提取干净的电话号码
  const phoneNumber = match[0].replace(/[-\s]/g, '');
  
  if (info.menuItemId === "addPhoneNumber") {
    // 打开添加联系人的弹窗
    chrome.tabs.sendMessage(tab.id, {
      action: "openPopupForAdd",
      phoneNumber: phoneNumber
    });
  } 
  else if (info.menuItemId === "searchPhoneNumber") {
    // 查询电话号码
    chrome.storage.sync.get('phoneContacts', function(data) {
      const contacts = data.phoneContacts || {};
      
      if (contacts[phoneNumber]) {
        chrome.tabs.sendMessage(tab.id, {
          action: "showNotification",
          message: `联系人: ${contacts[phoneNumber]}`,
          type: "success"
        });
      } else {
        chrome.tabs.sendMessage(tab.id, {
          action: "showNotification",
          message: "未找到匹配的联系人",
          type: "error"
        });
      }
    });
  }
  else if (info.menuItemId === "copyPhoneNumber") {
    // 发送消息到内容脚本复制电话号码
    chrome.tabs.sendMessage(tab.id, {
      action: "copyPhoneNumber",
      phoneNumber: phoneNumber
    });
  }
});

// 处理快捷键
chrome.commands.onCommand.addListener(function(command) {
  if (command === "add_number") {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: "getSelectedText"}, function(response) {
        if (response && response.text) {
          const selection = response.text.trim();
          const phoneRegex = /(\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}|\d{11})/;
          const match = selection.match(phoneRegex);
          
          if (match) {
            const phoneNumber = match[0].replace(/[-\s]/g, '');
            chrome.tabs.sendMessage(tabs[0].id, {
              action: "openPopupForAdd",
              phoneNumber: phoneNumber
            });
          } else {
            chrome.tabs.sendMessage(tabs[0].id, {
              action: "showNotification",
              message: "请先选择一个有效的电话号码",
              type: "error"
            });
          }
        }
      });
    });
  }
});

// 处理来自内容脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === "openPopup") {
    chrome.action.openPopup();
    sendResponse({success: true});
  }
}); 