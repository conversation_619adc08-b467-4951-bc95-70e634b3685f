<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>电话号码助手</title>
  <style>
    body {
      width: 350px;
      padding: 15px;
      font-family: Arial, sans-serif;
    }
    .tab {
      display: none;
    }
    .active {
      display: block;
    }
    .tab-buttons {
      display: flex;
      margin-bottom: 15px;
    }
    .tab-button {
      flex: 1;
      padding: 8px;
      background-color: #f1f1f1;
      border: none;
      cursor: pointer;
    }
    .tab-button.active {
      background-color: #ddd;
    }
    input, textarea, button {
      margin: 5px 0;
      padding: 8px;
      width: 100%;
      box-sizing: border-box;
    }
    textarea {
      height: 100px;
      resize: vertical;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f1f1f1;
    }
    .search-result {
      margin-top: 10px;
      padding: 10px;
      border: 1px solid #ddd;
      background-color: #f9f9f9;
    }
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <h2>电话号码助手</h2>
  
  <div class="tab-buttons">
    <button class="tab-button active" data-tab="search">查询</button>
    <button class="tab-button" data-tab="add">添加</button>
    <button class="tab-button" data-tab="import">导入/导出</button>
    <button class="tab-button" data-tab="list">列表</button>
  </div>
  
  <div id="search" class="tab active">
    <input type="text" id="search-input" placeholder="请输入电话号码">
    <button id="search-button">查询</button>
    <div id="search-result" class="search-result hidden">
      <p>电话号码: <span id="result-number"></span></p>
      <p>姓名: <span id="result-name"></span></p>
    </div>
  </div>
  
  <div id="add" class="tab">
    <input type="text" id="add-number" placeholder="电话号码">
    <input type="text" id="add-name" placeholder="姓名">
    <button id="add-button">添加</button>
    <p id="add-status"></p>
  </div>
  
  <div id="import" class="tab">
    <h3>导入数据</h3>
    <p>格式: 每行一条记录，号码和姓名用逗号分隔</p>
    <textarea id="import-data" placeholder="13800000000,张三&#10;13900000000,李四"></textarea>
    <button id="import-button">导入</button>
    
    <h3>导出数据</h3>
    <button id="export-button">导出所有数据</button>
    <textarea id="export-data" readonly></textarea>
  </div>
  
  <div id="list" class="tab">
    <table id="contacts-table">
      <thead>
        <tr>
          <th>电话号码</th>
          <th>姓名</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody id="contacts-list">
        <!-- 动态填充 -->
      </tbody>
    </table>
  </div>

  <script src="popup.js"></script>
</body>
</html> 