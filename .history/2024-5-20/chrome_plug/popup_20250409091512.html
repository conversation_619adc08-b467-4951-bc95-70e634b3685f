<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电话号码识别插件</title>
  <style>
    /* Font Awesome 基础样式 */
    .fas, .far {
      display: inline-block;
      font-style: normal;
      font-variant: normal;
      text-rendering: auto;
      line-height: 1;
      font-family: Arial, sans-serif;
      font-weight: 900;
      -moz-osx-font-smoothing: grayscale;
      -webkit-font-smoothing: antialiased;
    }
    
    /* 图标代码 */
    .fa-search:before { content: "🔍"; }
    .fa-user-plus:before { content: "👤+"; }
    .fa-file-import:before { content: "📥"; }
    .fa-address-book:before { content: "📒"; }
    .fa-phone-alt:before { content: "📱"; }
    .fa-user:before { content: "👤"; }
    .fa-phone:before { content: "📞"; }
    .fa-plus-circle:before { content: "➕"; }
    .fa-trash-alt:before { content: "🗑️"; }
    .fa-copy:before { content: "📋"; }
    .fa-download:before { content: "💾"; }
    .fa-save:before { content: "💾"; }
    .fa-upload:before { content: "📤"; }
    .fa-filter:before { content: "🔍"; }
    .fa-check-double:before { content: "✓✓"; }
    .fa-cog:before { content: "⚙️"; }
    .fa-star:before { content: "⭐"; }
    .fa-check-circle:before { content: "✓"; }
    .fa-exclamation-circle:before { content: "❗"; }
    .fa-exclamation-triangle:before { content: "⚠️"; }
    .fa-info-circle:before { content: "ℹ️"; }
    
    :root {
      --primary-color: #4a89dc;
      --primary-dark: #3a6fc6;
      --secondary-color: #f5f7fa;
      --success-color: #37bc9b;
      --danger-color: #e9573f;
      --warning-color: #f6bb42;
      --text-color: #434a54;
      --border-color: #e6e9ed;
    }
    
    body {
      width: 380px;
      padding: 0;
      font-family: 'Segoe UI', Arial, sans-serif;
      color: var(--text-color);
      background-color: #fff;
      margin: 0;
    }
    
    h2 {
      color: var(--primary-color);
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 10px;
      margin-top: 0;
      text-align: center;
    }
    
    .tab {
      display: none;
      animation: fadeIn 0.3s;
      padding: 0 15px 15px 15px;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .active {
      display: block;
    }
    
    .tab-buttons {
      display: flex;
      margin: 0 0 15px 0;
      border-radius: 0;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .tab-button {
      flex: 1;
      padding: 10px;
      background-color: var(--secondary-color);
      border: none;
      border-right: 1px solid var(--border-color);
      cursor: pointer;
      color: var(--text-color);
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .tab-button:last-child {
      border-right: none;
    }
    
    .tab-button i {
      margin-bottom: 5px;
      font-size: 1.2em;
    }
    
    .tab-button.active {
      background-color: var(--primary-color);
      color: white;
    }
    
    .tab-button:hover:not(.active) {
      background-color: #e6e9ed;
    }
    
    input, textarea, button:not(.tab-button):not(.settings-btn) {
      margin: 8px 0;
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      transition: border 0.3s;
    }
    
    input:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(74, 137, 220, 0.2);
    }
    
    textarea {
      height: 100px;
      resize: vertical;
    }
    
    button:not(.tab-button):not(.settings-btn) {
      background-color: var(--primary-color);
      color: white;
      border: none;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.3s;
    }
    
    button:not(.tab-button):not(.settings-btn):hover {
      background-color: var(--primary-dark);
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    th, td {
      padding: 12px 8px;
      text-align: left;
    }
    
    th {
      background-color: var(--primary-color);
      color: white;
      font-weight: normal;
    }
    
    tr:nth-child(even) {
      background-color: var(--secondary-color);
    }
    
    tr:hover {
      background-color: #e6e9ed;
    }
    
    .search-result {
      margin-top: 15px;
      padding: 15px;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      background-color: var(--secondary-color);
      transition: all 0.3s;
    }
    
    .hidden {
      display: none;
    }
    
    .notification {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      text-align: center;
      animation: fadeOut 3s forwards;
      animation-delay: 2s;
    }
    
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
    
    .success {
      background-color: var(--success-color);
      color: white;
    }
    
    .error {
      background-color: var(--danger-color);
      color: white;
    }
    
    .action-buttons {
      display: flex;
      gap: 5px;
    }
    
    .btn-delete {
      background-color: var(--danger-color);
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .btn-star {
      background-color: var(--warning-color);
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .filter-container {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }
    
    .filter-container input {
      padding: 8px;
      margin: 0;
    }
    
    .filter-container i {
      margin-right: 8px;
      color: var(--primary-color);
    }
    
    .import-options {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    
    .import-options button {
      width: 48%;
    }
    
    .export-options {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    
    .export-options button {
      width: 48%;
    }
    
    .featured {
      background-color: #fff8e1;
    }
    
    .settings-btn {
      position: absolute;
      top: 15px;
      right: 15px;
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 1.2em;
      z-index: 10;
    }
    
    /* 适应性强的顶部栏 */
    .app-header {
      background-color: var(--primary-color);
      color: white;
      padding: 15px;
      text-align: center;
      position: relative;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      margin-bottom: 0;
    }
    
    .app-title {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .app-title span.icon {
      margin-right: 10px;
      font-size: 1.2em;
    }
    
    #notification-container {
      padding: 0 15px;
    }
    
    .custom-fields {
      margin-top: 10px;
    }
    
    .add-field-form {
      display: flex;
      margin-bottom: 10px;
    }
    
    .add-field-form input {
      flex: 1;
      padding: 6px 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
      margin-right: 5px;
    }
    
    .add-field-form button {
      padding: 6px 10px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .fields-list {
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 5px;
    }
    
    .field-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px;
      border-bottom: 1px solid #eee;
    }
    
    .field-item:last-child {
      border-bottom: none;
    }
    
    .btn-delete-field {
      background: none;
      border: none;
      color: #f44336;
      cursor: pointer;
      font-size: 14px;
    }
    
    .edit-popup {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      padding: 20px;
      width: 300px;
      z-index: 1000;
    }
    
    .edit-form {
      margin-bottom: 15px;
    }
    
    .form-group {
      margin-bottom: 10px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
    }
    
    .form-group input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    .button-group {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
    
    .btn-save {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .btn-cancel {
      background-color: #f1f1f1;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
    }
    
    /* 调整表格样式适应动态列 */
    #contacts-table {
      table-layout: auto;
      min-width: 100%;
    }
    
    #contacts-table th, #contacts-table td {
      padding: 8px 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    #contacts-table th:first-child,
    #contacts-table td:first-child {
      width: 30px;
    }
    
    #contacts-table th:nth-child(2),
    #contacts-table td:nth-child(2) {
      width: 120px;
    }
    
    #contacts-table th:nth-child(3),
    #contacts-table td:nth-child(3) {
      width: 120px;
    }
    
    #contacts-table th:last-child,
    #contacts-table td:last-child {
      width: 100px;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <header class="app-header">
      <h1 class="app-title">电话号码识别</h1>
      <button class="settings-btn" id="settings-btn">⚙️</button>
    </header>
    
    <div id="notification-container"></div>
    
    <div class="tab-container">
      <div class="tab-buttons">
        <button class="tab-button active" data-tab="contacts-tab">联系人</button>
        <button class="tab-button" data-tab="fields-tab">自定义字段</button>
        <button class="tab-button" data-tab="settings-tab">设置</button>
      </div>
      
      <div class="tab-content">
        <!-- 联系人标签页 -->
        <div class="tab active" id="contacts-tab">
          <div class="search-bar">
            <input type="text" id="search-input" placeholder="搜索联系人...">
            <button id="search-button"><i class="fas fa-search"></i> 搜索</button>
            <button id="add-contact-btn"><i class="fas fa-user-plus"></i> 添加联系人</button>
          </div>
          
          <div id="search-result" class="search-result hidden">
            <div class="result-header">
              <b>电话:</b> <span id="result-number"></span>
            </div>
            <div class="result-body">
              <b>姓名:</b> <span id="result-name"></span>
            </div>
          </div>
          
          <div class="add-contact-form hidden" id="add-form-container">
            <form id="add-form">
              <div class="form-group">
                <label for="add-phone">电话号码</label>
                <input type="text" id="add-number" placeholder="输入电话号码" required>
              </div>
              <div class="form-group">
                <label for="add-name">姓名</label>
                <input type="text" id="add-name" placeholder="输入联系人姓名" required>
              </div>
              <div id="custom-fields-inputs"></div>
              <button type="submit" id="add-button">添加联系人</button>
            </form>
          </div>
          
          <div class="contacts-list-container">
            <div class="filter-container">
              <i class="fas fa-filter"></i>
              <input type="text" id="list-filter" placeholder="筛选联系人...">
            </div>
            
            <div class="list-actions">
              <button id="select-all-button"><i class="fas fa-check-double"></i> 全选</button>
              <button id="delete-selected-button"><i class="fas fa-trash-alt"></i> 删除选中</button>
            </div>
            
            <table id="contacts-table">
              <thead>
                <tr>
                  <th><input type="checkbox" id="select-all-checkbox"></th>
                  <th>电话号码</th>
                  <th>姓名</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody id="contacts-list"></tbody>
            </table>
          </div>
          
          <div class="import-export-container hidden" id="import-export-container">
            <div class="import-section">
              <h3>导入联系人</h3>
              <p class="import-help">按照"电话号码,姓名"格式输入，一行一个联系人</p>
              <textarea id="import-data" placeholder="例如：13812345678,张三"></textarea>
              <div class="import-options">
                <button id="import-button"><i class="fas fa-file-import"></i> 导入</button>
                <button id="import-clear-button">清空</button>
              </div>
            </div>
            
            <div class="export-section">
              <h3>导出联系人</h3>
              <textarea id="export-data" readonly></textarea>
              <div class="export-options">
                <button id="export-button"><i class="fas fa-download"></i> 生成</button>
                <button id="export-copy-button"><i class="fas fa-copy"></i> 复制</button>
              </div>
            </div>
            
            <div class="backup-section">
              <h3>备份与恢复</h3>
              <div class="backup-options">
                <button id="backup-button">备份数据</button>
                <button id="restore-button">恢复数据</button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 自定义字段标签页 -->
        <div class="tab" id="fields-tab">
          <div class="fields-container">
            <div class="fields-header">
              <h3>管理自定义字段</h3>
              <div class="add-field-form">
                <input type="text" id="new-field-name" placeholder="输入字段名称">
                <button id="add-field-button">添加字段</button>
              </div>
            </div>
            
            <div class="fields-list" id="custom-fields-list"></div>
            
            <p class="fields-hint">提示：添加自定义字段后，可以为联系人添加更多信息</p>
          </div>
        </div>
        
        <!-- 设置标签页 -->
        <div class="tab" id="settings-tab">
          <div class="settings-container">
            <h3>功能设置</h3>
            
            <div class="setting-item">
              <label for="auto-recognition">
                <input type="checkbox" id="autoRecognition">
                自动识别电话号码
              </label>
              <p class="setting-desc">启用后，页面上的电话号码将被自动识别并高亮显示</p>
            </div>
            
            <div class="setting-item">
              <label for="show-tooltips">
                <input type="checkbox" id="showTooltips">
                显示悬浮提示
              </label>
              <p class="setting-desc">启用后，鼠标悬停在识别的电话号码上时将显示操作提示</p>
            </div>
            
            <div class="setting-item">
              <label for="show-names">
                <input type="checkbox" id="showNames">
                显示联系人姓名
              </label>
              <p class="setting-desc">启用后，已保存的联系人电话号码将显示对应姓名</p>
            </div>
            
            <div class="setting-item">
              <label for="auto-fill">
                <input type="checkbox" id="autoFill">
                自动填充表单
              </label>
              <p class="setting-desc">启用后，在表单中输入电话号码时将自动提示填充联系人信息</p>
            </div>
            
            <div class="setting-item">
              <label for="auto-jump">
                <input type="checkbox" id="autoJump">
                自动跳转相关字段
              </label>
              <p class="setting-desc">启用后，填充一个字段后自动跳转到相关的下一个字段</p>
            </div>
            
            <div class="theme-switch-wrapper">
              <span>深色模式</span>
              <label class="theme-switch" for="dark-mode-toggle">
                <input type="checkbox" id="darkModeToggle">
                <span class="slider"></span>
              </label>
            </div>
            
            <!-- 开发者信息部分 -->
            <div class="about-section">
              <h3>关于插件</h3>
              <div id="developer-info">
                由专业团队开发的电话号码识别与管理工具，提高您的工作效率。
              </div>
              <div class="version-info">
                版本: <span id="version-number">1.0.0</span>
              </div>
              
              <div class="setting-item">
                <label for="developer-info-input">开发者信息</label>
                <textarea id="developer-info-input" rows="4" placeholder="输入开发者信息..."></textarea>
              </div>
            </div>
            
            <div class="quick-actions">
              <button class="action-button" id="export-data">导出数据</button>
              <button class="action-button" id="import-data">导入数据</button>
              <button class="action-button" id="clear-data">清除数据</button>
            </div>
            
            <button id="save-settings-button">保存设置</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>