// 电话号码正则表达式
const phoneRegex = /(\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}|\d{11})/g;

// 存储找到的电话号码
let foundNumbers = [];

// 主函数：扫描页面并处理电话号码
function scanForPhoneNumbers() {
  // 获取所有文本节点
  const textNodes = getTextNodes(document.body);
  
  // 扫描每个文本节点
  textNodes.forEach(node => {
    const text = node.nodeValue;
    const matches = text.match(phoneRegex);
    
    if (matches) {
      // 记录找到的号码
      matches.forEach(match => {
        const cleanNumber = match.replace(/[-\s]/g, '');
        if (foundNumbers.indexOf(cleanNumber) === -1) {
          foundNumbers.push(cleanNumber);
        }
      });
      
      // 替换文本中的电话号码
      let newText = text;
      matches.forEach(match => {
        const cleanNumber = match.replace(/[-\s]/g, '');
        newText = newText.replace(match, `<span class="phone-number" data-number="${cleanNumber}">${match}</span>`);
      });
      
      // 创建新元素并替换原文本节点
      const span = document.createElement('span');
      span.innerHTML = newText;
      node.parentNode.replaceChild(span, node);
    }
  });
  
  // 给所有电话号码添加悬停事件
  addHoverEffects();
  
  // 加载联系人数据并添加名字标签
  loadContactsAndAddLabels();
}

// 获取所有文本节点
function getTextNodes(node) {
  const textNodes = [];
  
  function getNodes(node) {
    if (node.nodeType === 3) {  // Text node
      textNodes.push(node);
    } else if (node.nodeType === 1 && node.nodeName !== 'SCRIPT' && node.nodeName !== 'STYLE') {  // Element node
      for (let i = 0; i < node.childNodes.length; i++) {
        getNodes(node.childNodes[i]);
      }
    }
  }
  
  getNodes(node);
  return textNodes;
}

// 添加悬停效果
function addHoverEffects() {
  const phoneElements = document.querySelectorAll('.phone-number');
  
  phoneElements.forEach(element => {
    element.style.cursor = 'pointer';
    element.style.position = 'relative';
    
    element.addEventListener('mouseenter', function() {
      const phoneNumber = this.getAttribute('data-number');
      const tooltip = document.createElement('div');
      tooltip.className = 'phone-tooltip';
      tooltip.style.position = 'absolute';
      tooltip.style.bottom = '100%';
      tooltip.style.left = '0';
      tooltip.style.padding = '5px';
      tooltip.style.backgroundColor = '#f9f9f9';
      tooltip.style.border = '1px solid #ddd';
      tooltip.style.borderRadius = '4px';
      tooltip.style.zIndex = '1000';
      
      // 获取联系人数据
      chrome.storage.sync.get('phoneContacts', function(data) {
        const contacts = data.phoneContacts || {};
        
        if (contacts[phoneNumber]) {
          tooltip.textContent = contacts[phoneNumber];
        } else {
          tooltip.textContent = '未知联系人';
        }
        
        element.appendChild(tooltip);
      });
    });
    
    element.addEventListener('mouseleave', function() {
      const tooltip = this.querySelector('.phone-tooltip');
      if (tooltip) {
        this.removeChild(tooltip);
      }
    });
  });
}

// 加载联系人数据并添加名字标签
function loadContactsAndAddLabels() {
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    
    // 添加标签到找到的电话号码
    const phoneElements = document.querySelectorAll('.phone-number');
    phoneElements.forEach(element => {
      const phoneNumber = element.getAttribute('data-number');
      
      if (contacts[phoneNumber]) {
        const nameLabel = document.createElement('span');
        nameLabel.className = 'name-label';
        nameLabel.textContent = ` (${contacts[phoneNumber]})`;
        nameLabel.style.color = 'green';
        nameLabel.style.fontWeight = 'bold';
        nameLabel.style.marginLeft = '5px';
        
        element.appendChild(nameLabel);
      }
    });
  });
}

// 监听存储变化，更新页面上的电话号码标签
chrome.storage.onChanged.addListener(function(changes, namespace) {
  if (namespace === 'sync' && changes.phoneContacts) {
    // 重新加载标签
    const nameLabels = document.querySelectorAll('.name-label');
    nameLabels.forEach(label => label.remove());
    loadContactsAndAddLabels();
  }
});

// 页面加载完成后运行
window.addEventListener('load', scanForPhoneNumbers);

// 定期重新扫描页面（应对动态加载的内容）
setInterval(scanForPhoneNumbers, 5000); 