<!--
 * @Author: hgxszhj <EMAIL>
 * @Date: 2025-04-09 05:11:07
 * @LastEditors: hgxszhj <EMAIL>
 * @LastEditTime: 2025-04-09 05:23:33
 * @FilePath: /my_project/2024-5-20/chrome_plug/README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 电话号码助手 Chrome 扩展

这是一个 Chrome 浏览器扩展，可以将网页上的电话号码与您保存的联系人姓名关联起来，方便识别电话号码的归属。

## 功能

- **查询**：输入电话号码查询对应的联系人姓名
- **添加**：手动添加电话号码与联系人姓名的对应关系
- **导入/导出**：批量导入导出电话号码与姓名的对应关系
- **自动识别**：自动识别网页上的电话号码，并显示对应的联系人姓名
- **悬停显示**：鼠标悬停在网页上的电话号码上，会显示对应的联系人姓名

## 安装方法

1. 下载本扩展的所有文件
2. 在 Chrome 浏览器中打开扩展管理页面：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含本扩展所有文件的文件夹

## 使用方法

### 查询联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 在弹出的窗口中，确保在"查询"标签下
3. 输入要查询的电话号码
4. 点击"查询"按钮，结果会显示在下方

### 添加联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"添加"标签
3. 输入电话号码和对应的姓名
4. 点击"添加"按钮保存

### 导入联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"导入/导出"标签
3. 在文本框中输入要导入的数据，格式为每行一条记录，电话号码和姓名用逗号分隔
   例如：`13800000000,张三`
4. 点击"导入"按钮

### 导出联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"导入/导出"标签
3. 点击"导出所有数据"按钮
4. 数据会显示在下方的文本框中，可以复制保存

### 查看所有联系人

1. 点击 Chrome 工具栏中的扩展图标
2. 切换到"列表"标签
3. 查看所有保存的电话号码和姓名，也可以在这里删除联系人

## 注意事项

- 本扩展使用 Chrome 的 storage.sync API 存储数据，数据会自动同步到您登录的所有 Chrome 浏览器
- 导入数据时，如果有重复的电话号码，新的姓名会覆盖原有的姓名
- 扩展会定期扫描页面以识别新加载的内容中的电话号码

## 图标替换说明

本扩展的图标文件夹中包含的是占位文件，您需要将它们替换为实际的 PNG 图标文件：
- icon16.png - 16x16 像素
- icon48.png - 48x48 像素
- icon128.png - 128x128 像素 