document.addEventListener('DOMContentLoaded', function() {
  // 初始化标签页切换
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabs = document.querySelectorAll('.tab');
  
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabId = button.getAttribute('data-tab');
      
      // 切换标签按钮状态
      tabButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');
      
      // 切换标签内容
      tabs.forEach(tab => tab.classList.remove('active'));
      document.getElementById(tabId).classList.add('active');
    });
  });

  // 添加自定义字段按钮
  document.getElementById('add-field-button').addEventListener('click', addCustomField);
  
  // 设置按钮事件
  document.getElementById('settings-btn').addEventListener('click', function() {
    tabButtons.forEach(btn => btn.classList.remove('active'));
    tabs.forEach(tab => tab.classList.remove('active'));
    document.getElementById('settings').classList.add('active');
  });
  
  // 加载保存的联系人数据
  loadContacts();
  
  // 加载设置
  loadSettings();
  
  // 查询功能
  document.getElementById('search-button').addEventListener('click', searchContact);
  document.getElementById('search-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchContact();
    }
  });
  
  // 添加联系人功能
  document.getElementById('add-button').addEventListener('click', addContact);
  
  // 导入/导出功能
  document.getElementById('import-button').addEventListener('click', importContacts);
  document.getElementById('import-clear-button').addEventListener('click', clearImportText);
  document.getElementById('export-button').addEventListener('click', exportContacts);
  document.getElementById('export-copy-button').addEventListener('click', copyToClipboard);
  document.getElementById('backup-button').addEventListener('click', backupData);
  document.getElementById('restore-button').addEventListener('click', restoreData);

  // 列表页面功能
  document.getElementById('list-filter').addEventListener('input', filterContacts);
  document.getElementById('select-all-checkbox').addEventListener('change', toggleSelectAll);
  document.getElementById('select-all-button').addEventListener('click', selectAllContacts);
  document.getElementById('delete-selected-button').addEventListener('click', deleteSelectedContacts);

  // 设置保存
  document.getElementById('save-settings-button').addEventListener('click', saveSettings);
  
  // 添加快捷键
  document.addEventListener('keydown', handleKeyboardShortcuts);
});

// 用户定义的自定义字段
let customFields = [];

// 加载自定义字段列表
function loadCustomFields(callback) {
  chrome.storage.sync.get('customFields', function(data) {
    customFields = data.customFields || [];
    if (typeof callback === 'function') {
      callback();
    }
  });
}

// 保存自定义字段列表
function saveCustomFields(fields, callback) {
  chrome.storage.sync.set({ 'customFields': fields }, function() {
    customFields = fields;
    if (typeof callback === 'function') {
      callback();
    }
  });
}

// 存储联系人数据
function saveContacts(contacts) {
  chrome.storage.sync.set({ 'phoneContacts': contacts }, function() {
    console.log('Contacts saved');
  });
}

// 加载联系人数据
function loadContacts() {
  loadCustomFields(function() {
    chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
      const contacts = data.phoneContacts || {};
      const starred = data.starredContacts || [];
      
      // 检查并转换旧数据格式
      let needsUpdate = false;
      for (const [phoneNumber, contactData] of Object.entries(contacts)) {
        if (typeof contactData === 'string') {
          contacts[phoneNumber] = {
            name: contactData,
            customData: {}
          };
          needsUpdate = true;
        }
      }
      
      if (needsUpdate) {
        saveContacts(contacts);
      }
      
      updateContactsList(contacts, starred);
    });
  });
}

// 更新联系人列表
function updateContactsList(contacts, starred = []) {
  const contactsList = document.getElementById('contacts-list');
  const filterText = document.getElementById('list-filter').value.toLowerCase();
  contactsList.innerHTML = '';
  
  // 更新表头以包含自定义字段
  updateTableHeader();
  
  // 转换为数组以便排序
  const contactsArray = Object.entries(contacts).map(([phoneNumber, contactData]) => {
    // 如果contactData是字符串，转换为对象格式
    const contact = typeof contactData === 'string' 
      ? { name: contactData, customData: {} } 
      : contactData;
    
    return {
      phoneNumber,
      name: typeof contactData === 'string' ? contactData : contactData.name,
      customData: typeof contactData === 'string' ? {} : (contactData.customData || {}),
      isStarred: starred.includes(phoneNumber)
    };
  });
  
  // 优先排序星标联系人
  contactsArray.sort((a, b) => {
    if (a.isStarred && !b.isStarred) return -1;
    if (!a.isStarred && b.isStarred) return 1;
    return a.name.localeCompare(b.name);
  });
  
  contactsArray.forEach(contact => {
    const { phoneNumber, name, customData, isStarred } = contact;
    
    // 过滤联系人
    if (filterText && !phoneNumber.includes(filterText) && !name.toLowerCase().includes(filterText)) {
      // 检查自定义字段是否匹配
      let matchesCustomField = false;
      for (const field of customFields) {
        if (customData[field] && customData[field].toLowerCase().includes(filterText)) {
          matchesCustomField = true;
          break;
        }
      }
      
      if (!matchesCustomField) {
        return;
      }
    }
    
    const row = document.createElement('tr');
    if (isStarred) {
      row.classList.add('featured');
    }
    
    // 复选框列
    const checkCell = document.createElement('td');
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.className = 'contact-checkbox';
    checkbox.setAttribute('data-number', phoneNumber);
    checkCell.appendChild(checkbox);
    
    // 号码列
    const numberCell = document.createElement('td');
    numberCell.textContent = phoneNumber;
    
    // 姓名列
    const nameCell = document.createElement('td');
    nameCell.textContent = name;
    
    // 自定义字段列
    const customCells = [];
    for (const field of customFields) {
      const customCell = document.createElement('td');
      customCell.textContent = customData[field] || '';
      customCells.push(customCell);
    }
    
    // 操作列
    const actionCell = document.createElement('td');
    const actionDiv = document.createElement('div');
    actionDiv.className = 'action-buttons';
    
    // 星标按钮
    const starButton = document.createElement('button');
    starButton.className = 'btn-star';
    starButton.innerHTML = isStarred ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
    starButton.title = isStarred ? '取消星标' : '设为星标';
    starButton.addEventListener('click', function() {
      toggleStarContact(phoneNumber);
    });
    
    // 编辑按钮
    const editButton = document.createElement('button');
    editButton.className = 'btn-edit';
    editButton.innerHTML = '<i class="fas fa-edit"></i>';
    editButton.title = '编辑';
    editButton.addEventListener('click', function() {
      editContact(phoneNumber, contact);
    });
    
    // 删除按钮
    const deleteButton = document.createElement('button');
    deleteButton.className = 'btn-delete';
    deleteButton.innerHTML = '<i class="fas fa-trash-alt"></i>';
    deleteButton.title = '删除';
    deleteButton.addEventListener('click', function() {
      deleteContact(phoneNumber);
    });
    
    actionDiv.appendChild(starButton);
    actionDiv.appendChild(editButton);
    actionDiv.appendChild(deleteButton);
    actionCell.appendChild(actionDiv);
    
    row.appendChild(checkCell);
    row.appendChild(numberCell);
    row.appendChild(nameCell);
    
    // 添加自定义字段单元格
    customCells.forEach(cell => {
      row.appendChild(cell);
    });
    
    row.appendChild(actionCell);
    
    contactsList.appendChild(row);
  });
}

// 更新表头
function updateTableHeader() {
  const tableHeader = document.querySelector('#contacts-table thead tr');
  
  // 清除现有的表头，除了第一列（复选框）
  while (tableHeader.children.length > 3) {
    tableHeader.removeChild(tableHeader.children[3]);
  }
  
  // 添加自定义字段表头
  for (const field of customFields) {
    const th = document.createElement('th');
    th.textContent = field;
    tableHeader.insertBefore(th, tableHeader.lastElementChild);
  }
}

// 编辑联系人信息
function editContact(phoneNumber, contact) {
  showEditPopup(phoneNumber, contact);
}

// 显示编辑联系人弹窗
function showEditPopup(phoneNumber, contact) {
  // 创建弹窗
  const popup = document.createElement('div');
  popup.className = 'edit-popup';
  
  // 创建基本表单字段
  let formContent = `
    <h3>编辑联系人</h3>
    <div class="edit-form">
      <div class="form-group">
        <label>电话号码</label>
        <input type="text" id="edit-number" value="${phoneNumber}" readonly>
      </div>
      <div class="form-group">
        <label>姓名</label>
        <input type="text" id="edit-name" value="${contact.name}">
      </div>
  `;
  
  // 添加自定义字段输入框
  customFields.forEach(field => {
    const value = contact.customData[field] || '';
    formContent += `
      <div class="form-group">
        <label>${field}</label>
        <input type="text" id="edit-${field}" value="${value}" data-field="${field}">
      </div>
    `;
  });
  
  // 添加按钮
  formContent += `
    </div>
    <div class="button-group">
      <button id="edit-save-btn" class="btn-save">保存</button>
      <button id="edit-cancel-btn" class="btn-cancel">取消</button>
    </div>
  `;
  
  popup.innerHTML = formContent;
  document.body.appendChild(popup);
  
  // 设置按钮事件
  document.getElementById('edit-cancel-btn').addEventListener('click', function() {
    document.body.removeChild(popup);
  });
  
  document.getElementById('edit-save-btn').addEventListener('click', function() {
    const name = document.getElementById('edit-name').value.trim();
    if (!name) {
      showNotification('姓名不能为空', 'error');
      return;
    }
    
    // 获取自定义字段值
    const customData = {};
    customFields.forEach(field => {
      const input = document.querySelector(`#edit-${field}`);
      if (input) {
        customData[field] = input.value.trim();
      }
    });
    
    // 更新联系人信息
    saveContact(phoneNumber, name, customData);
    document.body.removeChild(popup);
  });
}

// 保存联系人信息
function saveContact(phoneNumber, name, customData) {
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    
    // 更新联系人数据为对象格式
    contacts[phoneNumber] = {
      name: name,
      customData: customData
    };
    
    saveContacts(contacts);
    loadContacts();
    showNotification('联系人已更新', 'success');
  });
}

// 搜索联系人
function searchContact() {
  const phoneNumber = document.getElementById('search-input').value.trim();
  if (!phoneNumber) return;
  
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    const resultElement = document.getElementById('search-result');
    const numberElement = document.getElementById('result-number');
    const nameElement = document.getElementById('result-name');
    
    numberElement.textContent = phoneNumber;
    
    if (contacts[phoneNumber]) {
      nameElement.textContent = contacts[phoneNumber];
      resultElement.style.backgroundColor = 'var(--success-color)';
      resultElement.style.color = 'white';
    } else {
      nameElement.textContent = '未找到匹配的联系人';
      resultElement.style.backgroundColor = 'var(--secondary-color)';
      resultElement.style.color = 'var(--text-color)';
    }
    
    resultElement.classList.remove('hidden');
  });
}

// 添加联系人
function addContact() {
  const phoneNumber = document.getElementById('add-number').value.trim();
  const name = document.getElementById('add-name').value.trim();
  
  if (!phoneNumber || !name) {
    showNotification('电话号码和姓名不能为空', 'error');
    return;
  }
  
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    const isNew = !contacts[phoneNumber];
    
    // 保存为对象格式
    contacts[phoneNumber] = {
      name: name,
      customData: {}
    };
    
    saveContacts(contacts);
    loadContacts();
    
    // 清空输入框
    document.getElementById('add-number').value = '';
    document.getElementById('add-name').value = '';
    
    // 显示成功通知
    showNotification(
      isNew ? '联系人添加成功！' : '联系人更新成功！', 
      'success'
    );
  });
}

// 删除联系人
function deleteContact(phoneNumber) {
  if (confirm('确定要删除这个联系人吗？')) {
    chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
      const contacts = data.phoneContacts || {};
      const starred = data.starredContacts || [];
      
      if (contacts[phoneNumber]) {
        delete contacts[phoneNumber];
        saveContacts(contacts);
        
        // 如果是星标联系人，也要从星标列表中移除
        const starredIndex = starred.indexOf(phoneNumber);
        if (starredIndex !== -1) {
          starred.splice(starredIndex, 1);
          chrome.storage.sync.set({ 'starredContacts': starred });
        }
        
        loadContacts();
        showNotification('联系人已删除', 'success');
      }
    });
  }
}

// 导入联系人
function importContacts() {
  const importData = document.getElementById('import-data').value.trim();
  if (!importData) {
    showNotification('请输入要导入的数据', 'error');
    return;
  }
  
  const lines = importData.split('\n');
  const newContacts = {};
  let validCount = 0;
  
  lines.forEach(line => {
    const parts = line.split(',').map(item => item.trim());
    if (parts.length >= 2) {
      const phoneNumber = parts[0];
      const name = parts[1];
      const customData = {};
      
      // 处理可能的自定义字段
      for (let i = 0; i < customFields.length && i + 2 < parts.length; i++) {
        customData[customFields[i]] = parts[i + 2];
      }
      
      if (phoneNumber && name) {
        newContacts[phoneNumber] = {
          name: name,
          customData: customData
        };
        validCount++;
      }
    }
  });
  
  if (validCount === 0) {
    showNotification('没有有效的联系人数据', 'error');
    return;
  }
  
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    
    // 合并新旧联系人
    for (const [phoneNumber, contactData] of Object.entries(newContacts)) {
      if (contacts[phoneNumber] && typeof contacts[phoneNumber] === 'string') {
        // 转换旧格式
        contacts[phoneNumber] = {
          name: contacts[phoneNumber],
          customData: {}
        };
      }
      
      if (!contacts[phoneNumber]) {
        contacts[phoneNumber] = contactData;
      } else {
        // 合并自定义字段数据
        contacts[phoneNumber].name = contactData.name;
        contacts[phoneNumber].customData = {
          ...contacts[phoneNumber].customData,
          ...contactData.customData
        };
      }
    }
    
    saveContacts(contacts);
    loadContacts();
    
    // 清空导入文本框
    document.getElementById('import-data').value = '';
    showNotification(`成功导入 ${validCount} 个联系人！`, 'success');
  });
}

// 清空导入文本框
function clearImportText() {
  document.getElementById('import-data').value = '';
}

// 导出联系人
function exportContacts() {
  chrome.storage.sync.get('phoneContacts', function(data) {
    const contacts = data.phoneContacts || {};
    let exportText = '';
    
    // 创建表头
    exportText = `电话号码,姓名${customFields.length ? ',' + customFields.join(',') : ''}\n`;
    
    for (const [phoneNumber, contactData] of Object.entries(contacts)) {
      const name = typeof contactData === 'string' ? contactData : contactData.name;
      let line = `${phoneNumber},${name}`;
      
      // 添加自定义字段值
      if (typeof contactData !== 'string' && contactData.customData) {
        for (const field of customFields) {
          line += `,${contactData.customData[field] || ''}`;
        }
      } else {
        // 为旧数据格式添加空自定义字段
        line += ','.repeat(customFields.length);
      }
      
      exportText += line + '\n';
    }
    
    document.getElementById('export-data').value = exportText || '暂无联系人';
  });
}

// 复制导出数据到剪贴板
function copyToClipboard() {
  const exportText = document.getElementById('export-data');
  exportText.select();
  document.execCommand('copy');
  showNotification('已复制到剪贴板', 'success');
}

// 备份数据
function backupData() {
  chrome.storage.sync.get(['phoneContacts', 'starredContacts', 'settings'], function(data) {
    const backupData = JSON.stringify(data, null, 2);
    const blob = new Blob([backupData], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `phone_contacts_backup_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  });
}

// 恢复数据
function restoreData() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  
  input.onchange = e => {
    const file = e.target.files[0];
    const reader = new FileReader();
    
    reader.onload = event => {
      try {
        const data = JSON.parse(event.target.result);
        
        if (data.phoneContacts) {
          chrome.storage.sync.set(data, function() {
            loadContacts();
            loadSettings();
            showNotification('数据恢复成功！', 'success');
          });
        } else {
          showNotification('无效的备份文件', 'error');
        }
      } catch (e) {
        showNotification('无效的JSON格式', 'error');
      }
    };
    
    reader.readAsText(file);
  };
  
  input.click();
}

// 过滤联系人列表
function filterContacts() {
  chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
    const contacts = data.phoneContacts || {};
    const starred = data.starredContacts || [];
    updateContactsList(contacts, starred);
  });
}

// 切换选择所有联系人
function toggleSelectAll() {
  const isChecked = document.getElementById('select-all-checkbox').checked;
  const checkboxes = document.querySelectorAll('.contact-checkbox');
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = isChecked;
  });
}

// 选择所有联系人
function selectAllContacts() {
  document.getElementById('select-all-checkbox').checked = true;
  toggleSelectAll();
}

// 删除选中的联系人
function deleteSelectedContacts() {
  const selected = document.querySelectorAll('.contact-checkbox:checked');
  if (selected.length === 0) {
    showNotification('请先选择要删除的联系人', 'error');
    return;
  }
  
  if (confirm(`确定要删除选中的 ${selected.length} 个联系人吗？`)) {
    chrome.storage.sync.get(['phoneContacts', 'starredContacts'], function(data) {
      const contacts = data.phoneContacts || {};
      const starred = data.starredContacts || [];
      let updated = false;
      
      selected.forEach(checkbox => {
        const number = checkbox.getAttribute('data-number');
        if (contacts[number]) {
          delete contacts[number];
          updated = true;
          
          // 从星标中删除
          const starIndex = starred.indexOf(number);
          if (starIndex !== -1) {
            starred.splice(starIndex, 1);
          }
        }
      });
      
      if (updated) {
        chrome.storage.sync.set({ 
          'phoneContacts': contacts,
          'starredContacts': starred
        }, function() {
          loadContacts();
          showNotification(`已删除 ${selected.length} 个联系人`, 'success');
        });
      }
    });
  }
}

// 切换星标状态
function toggleStarContact(phoneNumber) {
  chrome.storage.sync.get('starredContacts', function(data) {
    const starred = data.starredContacts || [];
    const index = starred.indexOf(phoneNumber);
    
    if (index === -1) {
      starred.push(phoneNumber);
    } else {
      starred.splice(index, 1);
    }
    
    chrome.storage.sync.set({ 'starredContacts': starred }, function() {
      loadContacts();
    });
  });
}

// 显示通知
function showNotification(message, type) {
  const container = document.getElementById('notification-container');
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  container.appendChild(notification);
  
  // 3秒后自动移除通知
  setTimeout(() => {
    notification.style.opacity = '0';
    setTimeout(() => {
      container.removeChild(notification);
    }, 300);
  }, 3000);
}

// 加载设置
function loadSettings() {
  chrome.storage.sync.get('settings', function(data) {
    const settings = data.settings || {};
    document.getElementById('autoRecognition').checked = settings.autoRecognition !== false;
    document.getElementById('showTooltips').checked = settings.showTooltips !== false;
    document.getElementById('showNames').checked = settings.showNames !== false;
    document.getElementById('autoFill').checked = settings.autoFill !== false;
    document.getElementById('autoJump').checked = settings.autoJump !== false;
  });
}

// 保存设置
function saveSettings() {
  const settings = {
    autoRecognition: document.getElementById('autoRecognition').checked,
    showTooltips: document.getElementById('showTooltips').checked,
    showNames: document.getElementById('showNames').checked,
    autoFill: document.getElementById('autoFill').checked,
    autoJump: document.getElementById('autoJump').checked
  };
  
  chrome.storage.sync.set({ 'settings': settings }, function() {
    showNotification('设置已保存', 'success');
  });
}

// 初始化设置页面
function initSettingsTab() {
  // 加载设置
  loadSettings();
  
  // 设置事件监听器
  document.getElementById('autoRecognition').addEventListener('change', saveSettings);
  document.getElementById('showTooltips').addEventListener('change', saveSettings);
  document.getElementById('showNames').addEventListener('change', saveSettings);
  document.getElementById('autoFill').addEventListener('change', saveSettings);
  document.getElementById('autoJump').addEventListener('change', saveSettings);
}

// 处理键盘快捷键
function handleKeyboardShortcuts(e) {
  // Ctrl+F 或 Cmd+F (Mac) 聚焦到搜索框
  if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
    const activeTabId = document.querySelector('.tab.active').id;
    
    if (activeTabId === 'search') {
      document.getElementById('search-input').focus();
      e.preventDefault();
    } else if (activeTabId === 'list') {
      document.getElementById('list-filter').focus();
      e.preventDefault();
    }
  }
  
  // Ctrl+S 或 Cmd+S (Mac) 在添加页面时保存
  if ((e.ctrlKey || e.metaKey) && e.key === 's') {
    const activeTabId = document.querySelector('.tab.active').id;
    
    if (activeTabId === 'add') {
      addContact();
      e.preventDefault();
    }
  }
}

// 添加自定义字段
function addCustomField() {
  const fieldName = document.getElementById('new-field-name').value.trim();
  
  if (!fieldName) {
    showNotification('字段名称不能为空', 'error');
    return;
  }
  
  if (customFields.includes(fieldName)) {
    showNotification('字段名称已存在', 'error');
    return;
  }
  
  // 添加新字段
  customFields.push(fieldName);
  saveCustomFields(customFields, function() {
    // 更新字段列表显示
    updateCustomFieldsList();
    // 更新表格
    loadContacts();
    // 清空输入框
    document.getElementById('new-field-name').value = '';
    showNotification('已添加新字段', 'success');
  });
}

// 移除自定义字段
function removeCustomField(fieldName) {
  const index = customFields.indexOf(fieldName);
  if (index !== -1) {
    customFields.splice(index, 1);
    saveCustomFields(customFields, function() {
      // 更新字段列表显示
      updateCustomFieldsList();
      // 更新表格
      loadContacts();
      showNotification('已删除字段', 'success');
    });
  }
}

// 更新自定义字段列表UI
function updateCustomFieldsList() {
  const fieldsList = document.getElementById('custom-fields-list');
  fieldsList.innerHTML = '';
  
  customFields.forEach(field => {
    const item = document.createElement('div');
    item.className = 'field-item';
    
    const fieldText = document.createElement('span');
    fieldText.textContent = field;
    
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'btn-delete-field';
    deleteBtn.innerHTML = '<i class="fas fa-times"></i>';
    deleteBtn.addEventListener('click', function() {
      removeCustomField(field);
    });
    
    item.appendChild(fieldText);
    item.appendChild(deleteBtn);
    fieldsList.appendChild(item);
  });
}