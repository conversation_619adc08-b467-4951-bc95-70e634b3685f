"""
DeepSeek 集成模块 - 提供与DeepSeek API交互的功能
"""

import os
import json
import logging
import requests
from pathlib import Path
from typing import Optional, Dict, Any, Union

# 配置日志
logger = logging.getLogger(__name__)

class DeepSeekAssistant:
    """DeepSeek AI助手，提供智能文件命名、分类和内容分析功能"""
    
    # API端点
    API_BASE_URL = "https://api.deepseek.com/v1"
    
    def __init__(self, api_key: Optional[str] = None):
        """初始化DeepSeek助手
        
        Args:
            api_key: DeepSeek API密钥，如果不提供，会尝试从环境变量或配置文件加载
        """
        self.api_key = api_key or self._load_api_key()
        self._config_file = Path.home() / ".deepseek_config"
    
    def _load_api_key(self) -> Optional[str]:
        """从环境变量或配置文件加载API密钥"""
        # 首先检查环境变量
        api_key = os.environ.get("DEEPSEEK_API_KEY")
        if api_key:
            return api_key
            
        # 然后检查配置文件
        if self._config_file.exists():
            try:
                with open(self._config_file, "r") as f:
                    config = json.load(f)
                    return config.get("api_key")
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"无法从配置文件加载API密钥: {e}")
        
        return None
    
    def set_api_key(self, api_key: str) -> bool:
        """设置API密钥并保存到配置文件
        
        Args:
            api_key: DeepSeek API密钥
            
        Returns:
            bool: 设置成功返回True，否则返回False
        """
        if not api_key or not isinstance(api_key, str):
            logger.error("无效的API密钥")
            return False
            
        self.api_key = api_key.strip()
        
        # 保存到配置文件
        try:
            config = {"api_key": self.api_key}
            with open(self._config_file, "w") as f:
                json.dump(config, f)
            # 设置文件权限为只有当前用户可读写
            self._config_file.chmod(0o600)
            return True
        except IOError as e:
            logger.error(f"无法保存API密钥: {e}")
            return False
    
    def _make_api_request(self, endpoint: str, prompt: str, additional_params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """发送请求到DeepSeek API
        
        Args:
            endpoint: API端点路径
            prompt: 请求提示词
            additional_params: 额外的请求参数
            
        Returns:
            API响应或None（如果请求失败）
        """
        if not self.api_key:
            logger.error("未设置API密钥")
            return None
            
        url = f"{self.API_BASE_URL}/{endpoint}"
        
        # 准备请求参数
        params = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        # 添加额外参数
        if additional_params:
            params.update(additional_params)
            
        # 发送请求
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            response = requests.post(url, headers=headers, json=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            logger.error(f"API请求失败: {e}")
            return None
    
    def suggest_download_name(self, url: str, original_filename: str) -> Optional[str]:
        """为下载文件提供智能文件名建议
        
        Args:
            url: 下载URL
            original_filename: 原始文件名
            
        Returns:
            建议的文件名或None（如果请求失败）
        """
        prompt = f"""
        我需要为从以下URL下载的文件提供一个更好的文件名:
        URL: {url}
        当前文件名: {original_filename}
        
        请提供一个更具描述性、组织性和专业性的文件名。保留原始扩展名。只返回文件名，不要包含任何解释。
        """
        
        response = self._make_api_request("chat/completions", prompt)
        if not response:
            return None
            
        try:
            # 提取生成的文件名
            suggested_name = response.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            return suggested_name
        except (KeyError, IndexError) as e:
            logger.error(f"解析API响应失败: {e}")
            return None
    
    def categorize_download(self, url: str, filename: str) -> Optional[str]:
        """为下载文件提供智能分类建议
        
        Args:
            url: 下载URL
            filename: 文件名
            
        Returns:
            建议的分类路径或None（如果请求失败）
        """
        prompt = f"""
        我需要为以下下载文件确定最合适的分类目录:
        URL: {url}
        文件名: {filename}
        
        请分析URL和文件名，并提供一个最合适的文件分类目录路径。目录应该遵循通用的文件组织方式。
        例如: 文档/工作, 图片/风景, 软件/开发工具 等。
        只返回建议的目录路径，不要包含任何解释。
        """
        
        response = self._make_api_request("chat/completions", prompt)
        if not response:
            return None
            
        try:
            # 提取生成的分类
            category = response.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            return category
        except (KeyError, IndexError) as e:
            logger.error(f"解析API响应失败: {e}")
            return None
    
    def analyze_file_content(self, file_path: Path) -> Optional[str]:
        """分析文件内容并提供摘要
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容分析或None（如果请求失败）
        """
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            return None
            
        # 读取文件内容（最多前8KB以避免过大）
        try:
            file_name = file_path.name
            file_size = file_path.stat().st_size
            
            # 确定文件类型
            file_extension = file_path.suffix.lower()
            
            # 根据文件类型决定是否读取内容
            content_preview = ""
            if file_extension in ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv']:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content_preview = f.read(8192)  # 读取前8KB
                except UnicodeDecodeError:
                    content_preview = "(二进制内容预览不可用)"
            else:
                content_preview = "(二进制或不支持的文件类型)"
            
            prompt = f"""
            请分析以下文件的信息并提供简要摘要:
            
            文件名: {file_name}
            文件大小: {file_size} 字节
            文件类型: {file_extension}
            
            内容预览:
            {content_preview}
            
            请提供:
            1. 文件类型和格式的识别
            2. 文件内容的简要摘要
            3. 对文件用途的推测
            4. 任何值得注意的特点
            
            以易于理解的简明中文回答。
            """
            
            response = self._make_api_request("chat/completions", prompt, 
                                            {"temperature": 0.5, "max_tokens": 800})
            if not response:
                return None
                
            # 提取分析结果
            analysis = response.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            return analysis
            
        except IOError as e:
            logger.error(f"读取文件失败: {e}")
            return None

# 使用示例
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 初始化助手
    assistant = DeepSeekAssistant()
    
    # 设置API密钥（如果需要）
    # assistant.set_api_key("your-api-key")
    
    # 测试文件分析
    test_file = Path("README.md")
    if test_file.exists():
        result = assistant.analyze_file_content(test_file)
        print(f"文件分析结果: {result}")
    
    # 测试文件名建议
    suggested_name = assistant.suggest_download_name(
        "https://example.com/document123.pdf", 
        "document123.pdf"
    )
    print(f"建议的文件名: {suggested_name}")
    
    # 测试下载分类
    category = assistant.categorize_download(
        "https://example.com/document123.pdf",
        "document123.pdf"
    )
    print(f"下载分类: {category}") 