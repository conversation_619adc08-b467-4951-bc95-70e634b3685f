import os
import threading
import time
import logging
import platform
from tkinter import Tk, Label, Button, filedialog, messagebox, ttk, Entry, StringVar, Canvas, NW, IntVar, Frame, \
    Toplevel, Text, Scrollbar, VERTICAL, HORIZONTAL
from PIL import Image, ImageTk
import pytesseract


class OCRProcessor:
    def __init__(self, tessdata_prefix=None, lang='chi_sim'):
        if tessdata_prefix is None:
            tessdata_prefix = '/usr/local/share/tessdata/'

        if not os.path.exists(tessdata_prefix):
            raise FileNotFoundError(f"Tessdata directory '{tessdata_prefix}' not found. Please set the correct path.")

        os.environ['TESSDATA_PREFIX'] = tessdata_prefix
        self.lang = lang

    def process_images(self, image_dir, output_dir, num_threads, progress_callback=None):
        os.makedirs(output_dir, exist_ok=True)
        image_files = []
        for root, _, files in os.walk(image_dir):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif')):
                    image_files.append(os.path.join(root, file))

        total_files = len(image_files)
        self.total_files = total_files

        def process_chunk(chunk):
            for image_file in chunk:
                try:
                    image = Image.open(image_file)
                    text = pytesseract.image_to_string(image, lang=self.lang)
                    output_file = os.path.join(output_dir, os.path.basename(image_file) + ".txt")
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(text)
                except Exception as e:
                    logging.error(f"Error processing {image_file}: {e}")
                if progress_callback:
                    progress_callback()

        chunk_size = (total_files // num_threads) + 1
        chunks = [image_files[i:i + chunk_size] for i in range(0, total_files, chunk_size)]

        threads = []
        for chunk in chunks:
            thread = threading.Thread(target=process_chunk, args=(chunk,))
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

    def merge_results(self, output_dir, merged_output_file):
        import glob
        result_files = glob.glob(os.path.join(output_dir, '*.txt'))
        with open(merged_output_file, 'w', encoding='utf-8') as outfile:
            for fname in result_files:
                with open(fname, 'r', encoding='utf-8') as infile:
                    content = infile.read()
                    outfile.write(content + "\n")


class OCRApp:
    def __init__(self, root):
        self.root = root
        self.root.title("OCR 图片识别")
        self.create_widgets()
        self.image_dir = None
        self.output_dir = None
        self.merged_output_file = None
        self.image_files = []
        self.current_image_index = 0
        self.image_zoom = 1.0
        logging.basicConfig(filename='../ocr_errors.log', level=logging.ERROR)

    def create_widgets(self):
        # Configure grid layout
        self.root.columnconfigure(1, weight=1)
        self.root.rowconfigure(10, weight=1)

        Label(self.root, text="Tessdata 目录:").grid(row=0, column=0, sticky='e', padx=10, pady=5)
        self.tessdata_prefix_var = StringVar(value='/usr/local/share/tessdata/')
        self.tessdata_entry = Entry(self.root, textvariable=self.tessdata_prefix_var, width=50)
        self.tessdata_entry.grid(row=0, column=1, columnspan=2, padx=10, pady=5, sticky='ew')

        button_frame = Frame(self.root)
        button_frame.grid(row=1, column=0, columnspan=3, padx=10, pady=5, sticky='ew')

        self.select_image_button = Button(button_frame, text="选择图片目录", command=self.select_image_directory)
        self.select_image_button.grid(row=0, column=0, padx=5, pady=5)

        self.select_output_button = Button(button_frame, text="选择输出目录", command=self.select_output_directory)
        self.select_output_button.grid(row=0, column=1, padx=5, pady=5)

        Label(button_frame, text="线程数:").grid(row=0, column=2, sticky='e', padx=5, pady=5)
        self.num_threads_var = IntVar(value=1)
        self.num_threads_entry = Entry(button_frame, textvariable=self.num_threads_var, width=10)
        self.num_threads_entry.grid(row=0, column=3, padx=5, pady=5)

        self.execute_button = Button(self.root, text="开始处理", command=self.start_processing, state='disabled')
        self.execute_button.grid(row=2, column=0, columnspan=3, padx=10, pady=5, sticky='ew')

        self.progress_frame = Frame(self.root, height=30)
        self.progress_frame.grid(row=3, column=0, columnspan=3, padx=10, pady=10, sticky='ew')
        self.progress_frame.grid_propagate(False)
        self.progress = ttk.Progressbar(self.progress_frame, orient="horizontal", mode="determinate")
        self.progress.pack(fill='x', expand=True)

        self.progress_label = Label(self.root, text="当前进度: 0.00%")
        self.progress_label.grid(row=4, column=0, columnspan=3, padx=10, pady=5)

        canvas_frame = Frame(self.root)
        canvas_frame.grid(row=5, column=0, columnspan=3, padx=10, pady=5, sticky='nsew')

        self.canvas = Canvas(canvas_frame, bg='white')
        self.v_scroll = Scrollbar(canvas_frame, orient=VERTICAL, command=self.canvas.yview)
        self.h_scroll = Scrollbar(canvas_frame, orient=HORIZONTAL, command=self.canvas.xview)
        self.canvas.config(yscrollcommand=self.v_scroll.set, xscrollcommand=self.h_scroll.set)
        self.canvas.grid(row=0, column=0, sticky='nsew')
        self.v_scroll.grid(row=0, column=1, sticky='ns')
        self.h_scroll.grid(row=1, column=0, sticky='ew')

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        self.time_label = Label(self.root, text="处理时间: 0秒")
        self.time_label.grid(row=6, column=0, columnspan=3, padx=10, pady=5)

        control_frame = Frame(self.root)
        control_frame.grid(row=7, column=0, columnspan=3, padx=10, pady=5, sticky='ew')

        self.prev_button = Button(control_frame, text="上一张", command=self.prev_image)
        self.prev_button.grid(row=0, column=0, padx=5, pady=5)

        self.next_button = Button(control_frame, text="下一张", command=self.next_image)
        self.next_button.grid(row=0, column=1, padx=5, pady=5)

        self.zoom_in_button = Button(control_frame, text="放大", command=self.zoom_in)
        self.zoom_in_button.grid(row=0, column=2, padx=5, pady=5)

        self.zoom_out_button = Button(control_frame, text="缩小", command=self.zoom_out)
        self.zoom_out_button.grid(row=0, column=3, padx=5, pady=5)

        bottom_frame = Frame(self.root)
        bottom_frame.grid(row=8, column=0, columnspan=3, padx=10, pady=5, sticky='ew')

        self.view_file_button = Button(bottom_frame, text="查看合并文件", command=self.view_merged_file,
                                       state='disabled')
        self.view_file_button.grid(row=0, column=0, padx=5, pady=5)

        self.preview_button = Button(bottom_frame, text="预览图像", command=self.preview_image)
        self.preview_button.grid(row=0, column=1, padx=5, pady=5)

        self.close_button = Button(bottom_frame, text="关闭", command=self.root.quit)
        self.close_button.grid(row=0, column=2, padx=5, pady=5)

    def select_image_directory(self):
        self.image_dir = filedialog.askdirectory()
        if self.image_dir:
            self.preview_images_in_directory()
        if self.image_dir and self.output_dir:
            self.execute_button.config(state='normal')

    def select_output_directory(self):
        self.output_dir = filedialog.askdirectory()
        if self.output_dir and self.image_dir:
            self.execute_button.config(state='normal')

    def update_progress(self):
        self.completed_tasks += 1
        progress_percent = (self.completed_tasks / self.total_tasks) * 100
        self.progress['value'] = progress_percent
        self.progress_label.config(text=f"当前进度: {progress_percent:.2f}%")
        self.root.update_idletasks()

    def process_images(self):
        tessdata_prefix = self.tessdata_prefix_var.get()
        num_threads = self.num_threads_var.get()
        try:
            ocr_processor = OCRProcessor(tessdata_prefix)
        except FileNotFoundError as e:
            messagebox.showerror("错误", str(e))
            return

        if self.image_dir and self.output_dir:
            output_dir = self.output_dir
            merged_output_file = os.path.join(output_dir, "合并结果.txt")
            self.merged_output_file = merged_output_file

            self.completed_tasks = 0
            self.total_tasks = len([file for root, _, files in os.walk(self.image_dir) for file in files if
                                    file.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'))])

            self.progress['value'] = 0
            self.progress['maximum'] = 100

            start_time = time.time()
            ocr_processor.process_images(self.image_dir, output_dir, num_threads, self.update_progress)
            ocr_processor.merge_results(output_dir, merged_output_file)
            end_time = time.time()
            elapsed_time = end_time - start_time

            self.progress['value'] = 100
            self.progress_label.config(text="处理完成: 100.00%")
            self.time_label.config(text=f"处理时间: {elapsed_time:.2f}秒")
            messagebox.showinfo("完成", f"处理完成，结果已保存到 {merged_output_file}\n处理时间: {elapsed_time:.2f}秒")

            self.view_file_button.config(state='normal')

            # 播放系统完成声音提示
            self.play_completion_sound()

    def play_completion_sound(self):
        system = platform.system()
        if system == "Windows":
            import winsound
            winsound.MessageBeep(winsound.MB_OK)
        elif system == "Darwin":  # macOS
            os.system('afplay /System/Library/Sounds/Glass.aiff')
        else:
            print("无法识别操作系统，无法播放系统声音")

    def start_processing(self):
        processing_thread = threading.Thread(target=self.process_images)
        processing_thread.start()

    def preview_image(self):
        if not self.image_dir:
            messagebox.showerror("错误", "请先选择图片目录")
            return
        self.preview_images_in_directory()

    def preview_images_in_directory(self):
        image_files = [os.path.join(root, file) for root, _, files in os.walk(self.image_dir) for file in files if
                       file.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'))]
        if not image_files:
            messagebox.showerror("错误", "所选目录中没有图像文件")
            return

        self.image_files = image_files
        self.current_image_index = 0
        self.image_zoom = 1.0
        self.show_image(self.current_image_index)

    def show_image(self, index):
        if not self.image_files:
            return

        image_file = self.image_files[index]
        try:
            image = Image.open(image_file)
            width, height = image.size
            image = image.resize((int(width * self.image_zoom), int(height * self.image_zoom)), Image.LANCZOS)
            photo = ImageTk.PhotoImage(image)
            self.canvas.delete("all")  # 清除之前的图像
            self.canvas.create_image(0, 0, image=photo, anchor=NW)
            self.canvas.image = photo
            self.canvas.config(scrollregion=self.canvas.bbox("all"))  # 设置滚动区域
            self.root.title(f"图像预览 - {os.path.basename(image_file)}")
        except Exception as e:
            print(f"无法加载图像 {image_file}: {e}")

    def prev_image(self):
        if self.current_image_index > 0:
            self.current_image_index -= 1
            self.show_image(self.current_image_index)

    def next_image(self):
        if self.current_image_index < len(self.image_files) - 1:
            self.current_image_index += 1
            self.show_image(self.current_image_index)

    def zoom_in(self):
        self.image_zoom *= 1.2
        self.show_image(self.current_image_index)

    def zoom_out(self):
        self.image_zoom /= 1.2
        self.show_image(self.current_image_index)

    def view_merged_file(self):
        if self.merged_output_file and os.path.exists(self.merged_output_file):
            view_window = Toplevel(self.root)
            view_window.title(f"查看合并文件 - {os.path.basename(self.merged_output_file)}")

            text_widget = Text(view_window, wrap='word', width=100, height=30)
            text_widget.pack(expand=1, fill='both')

            with open(self.merged_output_file, 'r', encoding='utf-8') as file:
                content = file.read()
                text_widget.insert('1.0', content)
                text_widget.config(state='disabled')
        else:
            messagebox.showerror("错误", "合并文件不存在或路径不正确")


if __name__ == "__main__":
    root = Tk()
    app = OCRApp(root)
    root.mainloop()
