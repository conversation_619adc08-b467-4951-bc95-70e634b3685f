'''
Author: hgxszhj <EMAIL>
Date: 2024-06-15 16:03:18
LastEditors: hgxszhj <EMAIL>
LastEditTime: 2024-10-26 18:37:44
FilePath: /my_project/2024-5-20/videototxt/图片识别文字多线程_guiv02_01.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import os
import cv2
import numpy as np
from PIL import Image
import pytesseract
import threading
import time
from queue import Queue
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk
import multiprocessing

# ... (其他代码)

def process_frame(frame_queue, text_queue):
    while True:
        frame = frame_queue.get()
        if frame is None:
            break
        
        # 图像预处理优化
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        denoised = cv2.fastNlMeansDenoising(gray)
        thresh = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        # 使用Tesseract进行OCR
        text = pytesseract.image_to_string(thresh, lang='chi_sim+eng', config='--psm 6')
        text_queue.put(text)
        frame_queue.task_done()

def extract_frames_and_recognize(video_path, output_file, progress_callback):
    # ... (视频处理和多线程OCR的代码)
    
    num_processes = multiprocessing.cpu_count()
    pool = multiprocessing.Pool(processes=num_processes)
    
    # 使用进程池处理帧
    results = pool.imap(process_frame, frames)
    
    # ... (处理结果的代码)
    
    # 不需要手动关闭文件,上下文管理器会自动处理

# GUI部分
class VideoToTextGUI:
    def __init__(self, master):
        self.master = master
        master.title("视频文字提取工具")
        
        # ... (GUI组件的设置)
        
        self.progress = ttk.Progressbar(master, orient="horizontal", length=200, mode="determinate")
        self.progress.pack(pady=10)
    
    def select_video(self):
        # ... (选择视频文件的代码)

    def select_output(self):
        # ... (选择输出文件的代码)

    def start_extraction(self):
        if not self.video_path or not self.output_path:
            messagebox.showerror("错误", "请选择视频文件和输出文件")
            return
        
        self.progress["value"] = 0
        self.master.update_idletasks()
        
        # 修改extract_frames_and_recognize函数以接受进度回调
        extract_frames_and_recognize(self.video_path, self.output_path, self.update_progress)
    
    def update_progress(self, value):
        self.progress["value"] = value
        self.master.update_idletasks()

# 主程序
if __name__ == "__main__":
    root = tk.Tk()
    gui = VideoToTextGUI(root)
    root.mainloop()
