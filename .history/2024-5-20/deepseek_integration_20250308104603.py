"""
DeepSeek API集成模块 - 为下载器应用添加智能功能
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

# 尝试导入OpenAI库
try:
    from openai import OpenAI
except ImportError:
    logging.warning("OpenAI库未安装，请使用 pip install openai 安装")

class DeepSeekAssistant:
    """DeepSeek AI助手集成类"""
    
    def __init__(self, api_key: Optional[str] = None, config_file: str = "config.json"):
        """
        初始化DeepSeek助手
        
        Args:
            api_key: DeepSeek API密钥，如果为None则从环境变量或配置文件读取
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.api_key = api_key or os.environ.get("DEEPSEEK_API_KEY")
        
        # 如果环境变量中没有API密钥，尝试从配置文件读取
        if not self.api_key:
            self._load_api_key_from_config()
            
        self.client = None
        if self.api_key:
            self._initialize_client()
    
    def _load_api_key_from_config(self) -> None:
        """从配置文件加载API密钥"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.api_key = config.get("deepseek_api_key")
        except Exception as e:
            logging.error(f"加载API密钥失败: {str(e)}")
    
    def _initialize_client(self) -> None:
        """初始化DeepSeek客户端"""
        if not self.api_key:
            logging.warning("未设置DeepSeek API密钥，AI功能将不可用")
            return
            
        try:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url="https://api.deepseek.com"
            )
            logging.info("DeepSeek客户端初始化成功")
        except Exception as e:
            logging.error(f"初始化DeepSeek客户端失败: {str(e)}")
            self.client = None
    
    def set_api_key(self, api_key: str) -> bool:
        """
        设置API密钥并保存到配置
        
        Args:
            api_key: DeepSeek API密钥
            
        Returns:
            bool: 设置是否成功
        """
        self.api_key = api_key
        
        # 更新配置文件
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            
            config["deepseek_api_key"] = api_key
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4)
            
            # 重新初始化客户端
            self._initialize_client()
            return True
        except Exception as e:
            logging.error(f"保存API密钥失败: {str(e)}")
            return False
    
    def analyze_file_content(self, file_path: Path) -> Optional[str]:
        """
        分析文件内容，提供摘要和建议
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 分析结果，如果失败则返回None
        """
        if not self.client:
            return "AI功能未启用，请先设置DeepSeek API密钥"
            
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(10000)  # 限制内容大小
            
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个文件分析助手，请分析提供的文件内容并给出简短摘要。"},
                    {"role": "user", "content": f"请分析以下文件内容并提供简短摘要：\n\n{content}"}
                ],
                stream=False
            )
            
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"分析文件内容失败: {str(e)}")
            return f"分析失败: {str(e)}"
    
    def suggest_download_name(self, url: str, original_name: str) -> Optional[str]:
        """
        根据URL和原始文件名建议更好的文件名
        
        Args:
            url: 下载URL
            original_name: 原始文件名
            
        Returns:
            Optional[str]: 建议的文件名，如果失败则返回None
        """
        if not self.client:
            return None
            
        try:
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个文件命名助手，请根据URL和原始文件名建议更好的文件名。"},
                    {"role": "user", "content": f"URL: {url}\n原始文件名: {original_name}\n请建议一个更好的文件名（保留原始扩展名）"}
                ],
                stream=False
            )
            
            suggested_name = response.choices[0].message.content.strip()
            
            # 确保保留原始扩展名
            original_ext = os.path.splitext(original_name)[1]
            if original_ext and not suggested_name.endswith(original_ext):
                suggested_name = os.path.splitext(suggested_name)[0] + original_ext
                
            return suggested_name
        except Exception as e:
            logging.error(f"建议文件名失败: {str(e)}")
            return None
    
    def categorize_download(self, url: str, filename: str) -> Optional[str]:
        """
        根据URL和文件名对下载进行分类
        
        Args:
            url: 下载URL
            filename: 文件名
            
        Returns:
            Optional[str]: 建议的分类，如果失败则返回None
        """
        if not self.client:
            return None
            
        try:
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": "你是一个文件分类助手，请根据URL和文件名对下载进行分类。"},
                    {"role": "user", "content": f"URL: {url}\n文件名: {filename}\n请给出一个简短的分类名称（如：文档、图片、视频等）"}
                ],
                stream=False
            )
            
            return response.choices[0].message.content.strip()
        except Exception as e:
            logging.error(f"分类下载失败: {str(e)}")
            return None

# 使用示例
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 初始化助手
    assistant = DeepSeekAssistant()
    
    # 设置API密钥（如果需要）
    # assistant.set_api_key("your-api-key")
    
    # 测试文件分析
    test_file = Path("README.md")
    if test_file.exists():
        result = assistant.analyze_file_content(test_file)
        print(f"文件分析结果: {result}")
    
    # 测试文件名建议
    suggested_name = assistant.suggest_download_name(
        "https://example.com/document123.pdf", 
        "document123.pdf"
    )
    print(f"建议的文件名: {suggested_name}")
    
    # 测试下载分类
    category = assistant.categorize_download(
        "https://example.com/document123.pdf",
        "document123.pdf"
    )
    print(f"下载分类: {category}") 