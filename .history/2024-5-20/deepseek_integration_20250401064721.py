"""
DeepSeek 集成模块 - 提供与DeepSeek API交互的功能
模拟版本 - 用于演示
"""

import os
import json
import logging
import time
import random
from pathlib import Path
from typing import Optional, Dict, Any, Union

# 配置日志
logger = logging.getLogger(__name__)

class DeepSeekAssistant:
    """DeepSeek AI助手，提供智能文件命名、分类和内容分析功能"""
    
    def __init__(self, api_key: Optional[str] = None):
        """初始化DeepSeek助手
        
        Args:
            api_key: DeepSeek API密钥，如果不提供，会尝试从环境变量或配置文件加载
        """
        self.api_key = api_key or self._load_api_key()
        self._config_file = Path.home() / ".deepseek_config"
    
    def _load_api_key(self) -> Optional[str]:
        """从环境变量或配置文件加载API密钥"""
        # 首先检查环境变量
        api_key = os.environ.get("DEEPSEEK_API_KEY")
        if api_key:
            return api_key
            
        # 然后检查配置文件
        if self._config_file.exists():
            try:
                with open(self._config_file, "r") as f:
                    config = json.load(f)
                    return config.get("api_key")
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"无法从配置文件加载API密钥: {e}")
        
        return None
    
    def set_api_key(self, api_key: str) -> bool:
        """设置API密钥并保存到配置文件
        
        Args:
            api_key: DeepSeek API密钥
            
        Returns:
            bool: 设置成功返回True，否则返回False
        """
        if not api_key or not isinstance(api_key, str):
            logger.error("无效的API密钥")
            return False
            
        self.api_key = api_key.strip()
        
        # 保存到配置文件
        try:
            config = {"api_key": self.api_key}
            with open(self._config_file, "w") as f:
                json.dump(config, f)
            # 设置文件权限为只有当前用户可读写
            try:
                self._config_file.chmod(0o600)
            except:
                pass  # 忽略权限设置失败
            return True
        except IOError as e:
            logger.error(f"无法保存API密钥: {e}")
            return False
    
    def _mock_api_response(self, prompt: str) -> Dict[str, Any]:
        """模拟API响应（用于演示）
        
        Args:
            prompt: 请求提示词
            
        Returns:
            模拟的API响应
        """
        # 模拟响应延迟
        time.sleep(random.uniform(0.5, 1.5))
        
        # 根据提示词内容生成不同的响应
        if "文件名" in prompt:
            content = self._generate_filename_suggestion(prompt)
        elif "分类目录" in prompt:
            content = self._generate_category_suggestion(prompt)
        elif "文件的信息" in prompt:
            content = self._generate_file_analysis(prompt)
        else:
            content = "我无法处理这个请求。"
        
        # 模拟API响应格式
        return {
            "id": f"mock-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "deepseek-chat",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": content
                    },
                    "finish_reason": "stop"
                }
            ]
        }
    
    def _generate_filename_suggestion(self, prompt: str) -> str:
        """生成文件名建议"""
        lines = prompt.strip().split('\n')
        original_filename = ""
        url = ""
        
        for line in lines:
            if "当前文件名:" in line:
                original_filename = line.split("当前文件名:")[1].strip()
            elif "URL:" in line:
                url = line.split("URL:")[1].strip()
        
        if not original_filename:
            return "无法识别原始文件名"
        
        # 简单的文件名生成逻辑
        name, ext = os.path.splitext(original_filename)
        
        # 根据URL和原始名称生成更好的名称
        if "github" in url.lower():
            return f"GitHub项目-{name}{ext}"
        elif "document" in url.lower() or "doc" in url.lower():
            return f"文档-{name}{ext}"
        elif "image" in url.lower() or "photo" in url.lower():
            return f"图像-{name}{ext}"
        elif "video" in url.lower():
            return f"视频-{name}{ext}"
        elif "download" in url.lower():
            return f"下载-{name}{ext}"
        else:
            # 添加日期前缀
            import datetime
            today = datetime.datetime.today().strftime("%Y%m%d")
            return f"{today}-{name}{ext}"
    
    def _generate_category_suggestion(self, prompt: str) -> str:
        """生成分类建议"""
        lines = prompt.strip().split('\n')
        filename = ""
        url = ""
        
        for line in lines:
            if "文件名:" in line:
                filename = line.split("文件名:")[1].strip()
            elif "URL:" in line:
                url = line.split("URL:")[1].strip()
        
        if not filename:
            return "无法识别文件名"
        
        # 根据文件扩展名和URL生成分类
        _, ext = os.path.splitext(filename)
        ext = ext.lower()
        
        if ext in ['.doc', '.docx', '.pdf', '.txt', '.md']:
            if "work" in url.lower() or "work" in filename.lower():
                return "文档/工作"
            elif "study" in url.lower() or "study" in filename.lower():
                return "文档/学习"
            else:
                return "文档/其他"
        elif ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            if "photo" in url.lower() or "photo" in filename.lower():
                return "图片/照片"
            elif "screenshot" in url.lower() or "screenshot" in filename.lower():
                return "图片/截图"
            else:
                return "图片/其他"
        elif ext in ['.mp4', '.avi', '.mov', '.mkv', '.flv']:
            return "视频/娱乐"
        elif ext in ['.mp3', '.wav', '.flac', '.ogg']:
            return "音频/音乐"
        elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return "压缩文件"
        elif ext in ['.exe', '.msi', '.dmg', '.app']:
            return "软件/应用程序"
        elif ext in ['.py', '.js', '.html', '.css', '.java', '.cpp']:
            return "编程/源代码"
        else:
            return "其他/未分类"
    
    def _generate_file_analysis(self, prompt: str) -> str:
        """生成文件分析"""
        lines = prompt.strip().split('\n')
        file_name = ""
        file_size = ""
        file_extension = ""
        content_preview = ""
        
        reading_content = False
        for line in lines:
            if reading_content:
                content_preview += line + "\n"
            elif "文件名:" in line:
                file_name = line.split("文件名:")[1].strip()
            elif "文件大小:" in line:
                file_size = line.split("文件大小:")[1].strip()
            elif "文件类型:" in line:
                file_extension = line.split("文件类型:")[1].strip()
            elif "内容预览:" in line:
                reading_content = True
        
        if not file_name or not file_extension:
            return "无法识别文件信息"
        
        # 根据文件类型和内容预览生成分析
        file_extension = file_extension.lower()
        
        analysis = f"## 文件分析报告\n\n"
        analysis += f"### 基本信息\n"
        analysis += f"- **文件名**: {file_name}\n"
        analysis += f"- **大小**: {file_size}\n"
        analysis += f"- **类型**: {file_extension}\n\n"
        
        analysis += f"### 内容摘要\n"
        
        if file_extension in ['.py']:
            analysis += "这是一个Python源代码文件，包含了程序逻辑实现。"
            if "import" in content_preview:
                analysis += "文件中导入了一些Python模块，这表明它依赖于其他库。"
            if "class" in content_preview:
                analysis += "代码中定义了类结构，采用了面向对象编程方法。"
            if "def" in content_preview:
                analysis += "包含多个函数定义，每个函数实现特定功能。"
                
        elif file_extension in ['.js']:
            analysis += "这是一个JavaScript源代码文件，用于网页交互功能实现。"
            
        elif file_extension in ['.html']:
            analysis += "这是一个HTML网页文件，定义了网页的结构和内容。"
            
        elif file_extension in ['.css']:
            analysis += "这是一个CSS样式表文件，用于定义网页的视觉样式。"
            
        elif file_extension in ['.json']:
            analysis += "这是一个JSON数据文件，包含了结构化的数据信息。"
            
        elif file_extension in ['.txt', '.md']:
            analysis += "这是一个文本文件，包含了纯文本内容。"
            
        elif file_extension in ['.jpg', '.jpeg', '.png', '.gif']:
            analysis += "这是一个图像文件，无法进行文本内容分析。"
            
        else:
            analysis += "这个文件类型无法提供详细内容分析。"
        
        analysis += "\n\n### 用途推测\n"
        analysis += "根据文件类型和内容，这个文件可能用于以下目的：\n"
        
        if file_extension in ['.py', '.js', '.java', '.cpp']:
            analysis += "- 软件开发或脚本编程\n"
            analysis += "- 可能是某个项目的组成部分\n"
            
        elif file_extension in ['.html', '.css']:
            analysis += "- 网站或网页应用开发\n"
            analysis += "- 用户界面设计\n"
            
        elif file_extension in ['.jpg', '.jpeg', '.png', '.gif']:
            analysis += "- 图像展示或存档\n"
            analysis += "- 可能用于网页、演示文稿或文档插图\n"
            
        elif file_extension in ['.doc', '.docx', '.pdf', '.txt', '.md']:
            analysis += "- 文档存储或信息记录\n"
            analysis += "- 可能包含重要的业务或个人信息\n"
            
        else:
            analysis += "- 无法确定具体用途\n"
        
        return analysis
    
    def suggest_download_name(self, url: str, original_filename: str) -> Optional[str]:
        """为下载文件提供智能文件名建议
        
        Args:
            url: 下载URL
            original_filename: 原始文件名
            
        Returns:
            建议的文件名或None（如果请求失败）
        """
        if not self.api_key:
            logger.error("未设置API密钥")
            return None
            
        prompt = f"""
        我需要为从以下URL下载的文件提供一个更好的文件名:
        URL: {url}
        当前文件名: {original_filename}
        
        请提供一个更具描述性、组织性和专业性的文件名。保留原始扩展名。只返回文件名，不要包含任何解释。
        """
        
        # 使用模拟API响应
        response = self._mock_api_response(prompt)
        if not response:
            return None
            
        try:
            # 提取生成的文件名
            suggested_name = response.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            return suggested_name
        except (KeyError, IndexError) as e:
            logger.error(f"解析API响应失败: {e}")
            return None
    
    def categorize_download(self, url: str, filename: str) -> Optional[str]:
        """为下载文件提供智能分类建议
        
        Args:
            url: 下载URL
            filename: 文件名
            
        Returns:
            建议的分类路径或None（如果请求失败）
        """
        if not self.api_key:
            logger.error("未设置API密钥")
            return None
            
        prompt = f"""
        我需要为以下下载文件确定最合适的分类目录:
        URL: {url}
        文件名: {filename}
        
        请分析URL和文件名，并提供一个最合适的文件分类目录路径。目录应该遵循通用的文件组织方式。
        例如: 文档/工作, 图片/风景, 软件/开发工具 等。
        只返回建议的目录路径，不要包含任何解释。
        """
        
        # 使用模拟API响应
        response = self._mock_api_response(prompt)
        if not response:
            return None
            
        try:
            # 提取生成的分类
            category = response.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            return category
        except (KeyError, IndexError) as e:
            logger.error(f"解析API响应失败: {e}")
            return None
    
    def analyze_file_content(self, file_path: Path) -> Optional[str]:
        """分析文件内容并提供摘要
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容分析或None（如果请求失败）
        """
        if not self.api_key:
            logger.error("未设置API密钥")
            return None
            
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            return None
            
        # 读取文件内容（最多前8KB以避免过大）
        try:
            file_name = file_path.name
            file_size = file_path.stat().st_size
            
            # 确定文件类型
            file_extension = file_path.suffix.lower()
            
            # 根据文件类型决定是否读取内容
            content_preview = ""
            if file_extension in ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv']:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content_preview = f.read(8192)  # 读取前8KB
                except UnicodeDecodeError:
                    content_preview = "(二进制内容预览不可用)"
            else:
                content_preview = "(二进制或不支持的文件类型)"
            
            prompt = f"""
            请分析以下文件的信息并提供简要摘要:
            
            文件名: {file_name}
            文件大小: {file_size} 字节
            文件类型: {file_extension}
            
            内容预览:
            {content_preview}
            
            请提供:
            1. 文件类型和格式的识别
            2. 文件内容的简要摘要
            3. 对文件用途的推测
            4. 任何值得注意的特点
            
            以易于理解的简明中文回答。
            """
            
            # 使用模拟API响应
            response = self._mock_api_response(prompt)
            if not response:
                return None
                
            # 提取分析结果
            analysis = response.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            return analysis
            
        except IOError as e:
            logger.error(f"读取文件失败: {e}")
            return None

# 使用示例
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 初始化助手
    assistant = DeepSeekAssistant()
    
    # 设置API密钥（如果需要）
    # assistant.set_api_key("your-api-key")
    
    # 测试文件分析
    test_file = Path("README.md")
    if test_file.exists():
        result = assistant.analyze_file_content(test_file)
        print(f"文件分析结果: {result}")
    
    # 测试文件名建议
    suggested_name = assistant.suggest_download_name(
        "https://example.com/document123.pdf", 
        "document123.pdf"
    )
    print(f"建议的文件名: {suggested_name}")
    
    # 测试下载分类
    category = assistant.categorize_download(
        "https://example.com/document123.pdf",
        "document123.pdf"
    )
    print(f"下载分类: {category}") 