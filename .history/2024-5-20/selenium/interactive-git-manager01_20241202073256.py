#!/usr/bin/env python3
import os
import sys
import subprocess
import getpass
from datetime import datetime

class InteractiveGitManager:
    def __init__(self):
        self.repo_path = self.get_repo_path()

    def clear_screen(self):
        """清空控制台屏幕"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def get_repo_path(self):
        """获取当前Git仓库的根目录"""
        try:
            return subprocess.check_output(
                ['git', 'rev-parse', '--show-toplevel'], 
                text=True
            ).strip()
        except subprocess.CalledProcessError:
            print("错误：当前不在Git仓库中")
            sys.exit(1)

    def run_command(self, command, cwd=None):
        """
        运行Git命令并返回输出
        
        :param command: 要执行的命令列表
        :param cwd: 执行命令的工作目录，默认为仓库根目录
        :return: 命令输出
        """
        try:
            return subprocess.check_output(
                command, 
                cwd=cwd or self.repo_path, 
                stderr=subprocess.STDOUT, 
                text=True
            ).strip()
        except subprocess.CalledProcessError as e:
            print(f"命令执行错误: {e.output}")
            return None

    def display_menu(self):
        """显示主菜单"""
        self.clear_screen()
        print("===== Git 交互式管理工具 =====")
        print("1. 查看仓库状态")
        print("2. 创建新分支")
        print("3. 切换分支")
        print("4. 提交变更")
        print("5. 推送分支")
        print("6. 查看分支列表")
        print("7. 合并分支")
        print("8. 创建标签")
        print("9. 查看提交历史")
        print("0. 退出")
        print("========================")

    def view_repo_status(self):
        """查看仓库状态"""
        print("\n===== 当前仓库状态 =====")
        status = self.run_command(['git', 'status', '-s'])
        print(status if status else "仓库无变更")
        input("\n按回车键返回主菜单...")

    def create_branch(self):
        """交互式创建分支"""
        print("\n===== 创建新分支 =====")
        base_branch = input("请输入基础分支名称 (默认: main): ") or 'main'
        
        # 切换到基础分支并更新
        self.run_command(['git', 'checkout', base_branch])
        self.run_command(['git', 'pull'])
        
        # 输入新分支名称
        while True:
            branch_name = input("请输入新分支名称: ").strip()
            if branch_name:
                break
            print("分支名称不能为空！")
        
        try:
            # 创建并切换到新分支
            result = self.run_command(['git', 'checkout', '-b', branch_name])
            
            if result is not None:
                print(f"\n成功创建并切换到新分支 '{branch_name}'")
        except Exception as e:
            print(f"创建分支时发生错误: {e}")
        
        input("\n按回车键返回主菜单...")

    def switch_branch(self):
        """交互式切换分支"""
        print("\n===== 可用分支列表 =====")
        branches = self.run_command(['git', 'branch', '-a']).split('\n')
        for i, branch in enumerate(branches, 1):
            print(f"{i}. {branch.strip()}")
        
        while True:
            try:
                choice = int(input("\n请选择要切换的分支序号: "))
                if 1 <= choice <= len(branches):
                    branch = branches[choice-1].strip().lstrip('*').strip()
                    # 处理远程分支
                    if branch.startswith('remotes/'):
                        branch = branch.split('/')[-1]
                    
                    self.run_command(['git', 'checkout', branch])
                    print(f"\n成功切换到分支 '{branch}'")
                    break
                else:
                    print("无效的序号，请重新选择")
            except ValueError:
                print("请输入有效的数字")
        
        input("\n按回车键返回主菜单...")

    def commit_changes(self):
        """交互式提交变更"""
        # 先显示状态
        print("\n===== 当前仓库状态 =====")
        status = self.run_command(['git', 'status', '-s'])
        print(status if status else "仓库无变更")
        
        # 确认是否提交
        confirm = input("\n是否要提交所有变更？(y/n): ").lower()
        if confirm != 'y':
            return
        
        # 暂存所有变更
        self.run_command(['git', 'add', '-A'])
        
        # 输入提交信息
        while True:
            message = input("请输入提交信息: ").strip()
            if message:
                break
            print("提交信息不能为空！")
        
        # 执行提交
        result = self.run_command(['git', 'commit', '-m', message])
        
        if result is not None:
            print(f"\n提交成功: {message}")
        
        input("\n按回车键返回主菜单...")

    def push_branch(self):
        """交互式推送分支"""
        # 获取当前分支
        current_branch = self.run_command(['git', 'rev-parse', '--abbrev-ref', 'HEAD'])
        
        print(f"\n当前分支：{current_branch}")
        confirm = input("是否推送当前分支到远程？(y/n): ").lower()
        
        if confirm == 'y':
            try:
                result = self.run_command(['git', 'push', '-u', 'origin', current_branch])
                if result is not None:
                    print(f"\n成功推送分支 '{current_branch}' 到远程仓库")
            except Exception as e:
                print(f"推送分支时发生错误: {e}")
        
        input("\n按回车键返回主菜单...")

    def view_branches(self):
        """查看分支列表"""
        print("\n===== 本地分支 =====")
        local_branches = self.run_command(['git', 'branch'])
        print(local_branches)
        
        print("\n===== 远程分支 =====")
        remote_branches = self.run_command(['git', 'branch', '-r'])
        print(remote_branches)
        
        input("\n按回车键返回主菜单...")

    def merge_branch(self):
        """交互式合并分支"""
        print("\n===== 可用分支列表 =====")
        branches = self.run_command(['git', 'branch']).split('\n')
        
        for i, branch in enumerate(branches, 1):
            print(f"{i}. {branch.strip()}")
        
        while True:
            try:
                # 选择要合并的分支
                choice = int(input("\n请选择要合并的分支序号: "))
                if 1 <= choice <= len(branches):
                    branch_to_merge = branches[choice-1].strip().lstrip('*').strip()
                    break
                else:
                    print("无效的序号，请重新选择")
            except ValueError:
                print("请输入有效的数字")
        
        confirm = input(f"\n确定要将 {branch_to_merge} 分支合并到当前分支吗？(y/n): ").lower()
        
        if confirm == 'y':
            try:
                result = self.run_command(['git', 'merge', branch_to_merge])
                print(f"\n成功合并 {branch_to_merge} 分支")
            except Exception as e:
                print(f"合并分支时发生错误: {e}")
        
        input("\n按回车键返回主菜单...")

    def create_tag(self):
        """交互式创建标签"""
        print("\n===== 创建标签 =====")
        tag_name = input("请输入标签名称: ").strip()
        tag_message = input("请输入标签描述 (可选): ").strip()
        
        try:
            if tag_message:
                result = self.run_command(['git', 'tag', '-a', tag_name, '-m', tag_message])
            else:
                result = self.run_command(['git', 'tag', tag_name])
            
            print(f"\n成功创建标签 {tag_name}")
        except Exception as e:
            print(f"创建标签时发生错误: {e}")
        
        input("\n按回车键返回主菜单...")

    def view_commit_history(self):
        """查看提交历史"""
        print("\n===== 提交历史 =====")
        # 显示最近10条提交记录
        history = self.run_command(['git', 'log', '-10', '--pretty=format:%h - %an, %ar : %s'])
        print(history)
        
        input("\n按回车键返回主菜单...")

    def run(self):
        """主交互循环"""
        while True:
            self.display_menu()
            
            try:
                choice = input("请选择操作 (0-9): ")
                
                # 根据用户选择执行相应操作
                if choice == '1':
                    self.view_repo_status()
                elif choice == '2':
                    self.create_branch()
                elif choice == '3':
                    self.switch_branch()
                elif choice == '4':
                    self.commit_changes()
                elif choice == '5':
                    self.push_branch()
                elif choice == '6':
                    self.view_branches()
                elif choice == '7':
                    self.merge_branch()
                elif choice == '8':
                    self.create_tag()
                elif choice == '9':
                    self.view_commit_history()
                elif choice == '0':
                    print("感谢使用Git管理工具，再见！")
                    break
                else:
                    print("无效的选择，请重新输入")
                    input("按回车键继续...")
            except KeyboardInterrupt:
                print("\n\n操作已取消")
                break
            except Exception as e:
                print(f"发生错误：{e}")
                input("按回车键继续...")

def main():
    try:
        git_manager = InteractiveGitManager()
        git_manager.run()
    except Exception as e:
        print(f"发生致命错误：{e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
