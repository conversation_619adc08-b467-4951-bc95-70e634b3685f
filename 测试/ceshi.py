from DrissionPage import ChromiumPage
import re

def analyze_video_stream(url):
    # 创建 ChromiumPage 对象
    page = ChromiumPage()

    try:
        # 访问页面
        page.get(url)
        print(f"Page title: {page.title}")

        # 等待页面加载（可以根据需要调整等待时间）
        page.wait.load_complete()

        # 获取页面源代码
        page_source = page.html
        print(f"Page source length: {len(page_source)}")

        # 查找可能的媒体URL
        media_urls = re.findall(r'https?://[^\s"\']+\.(?:m3u8|pdx|mp4|ts)', page_source)

        if media_urls:
            print("\nFound potential media URLs in page source:")
            for url in media_urls:
                print(url)
        else:
            print("\nNo potential media URLs found in page source")

        # 获取并分析网络请求
        print("\nAnalyzing network requests:")
        for request in page.listen.all:
            if 'm3u8' in request.url or 'pdx' in request.url:
                print(f"Potential video URL: {request.url}")
                # 如果需要，可以进一步分析请求的内容
                if request.response:
                    print(f"Response status: {request.response.status_code}")
                    print(f"Response content type: {request.response.headers.get('Content-Type')}")

        # 尝试查找和分析视频播放器元素
        video_elements = page.eles('video')
        if video_elements:
            print("\nFound video elements:")
            for video in video_elements:
                print(f"Video source: {video.src}")
                print(f"Video type: {video.type}")

    except Exception as e:
        print(f"An error occurred: {e}")

    finally:
        # 关闭浏览器
        page.quit()

if __name__ == "__main__":
    test_url = "https://www.luffycity.com/play/76266"
    analyze_video_stream(test_url)
