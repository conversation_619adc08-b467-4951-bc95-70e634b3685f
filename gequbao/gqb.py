import requests
import json
import parsel
from prettytable import PrettyTable

headers = {
    "referer": "https://www.gequbao.com/",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
key = input("请输入下载的歌曲名/歌手：  ").strip()
search_url = f'https://www.gequbao.com/s/{key}'
search_response = requests.get(url=search_url, headers=headers)
# 获取网页文本数据
html = search_response.text
tb = PrettyTable()
tb.field_names = ['序号', '歌名']
page = 0
info = []
# 解析数据
selector = parsel.Selector(html)
# 定位标签为row位置
rows = selector.css('.row')[1:-1]

for row in rows:
    title = row.css(' .text-jade::text ').get().strip()
    music_id = row.css('.music-link::attr(href)').get().split('/')[-1]
    dit = {
        "歌名": title,
        "ID": music_id
    }
    info.append(dit)
    tb.add_row([page, title])
    page += 1
print(tb)
num = input("请输入歌曲序号：")
download_id = info[int(num)]['ID']
download_title = info[int(num)]['歌名']

# 请求网址
url = f'https://www.gequbao.com/api/play_url?id={download_id}&json=1'
# 发送请求
response = requests.get(url=url, headers=headers)
json_data = response.json()
play_url = json_data['data']['url']
music_content = requests.get(url=play_url, headers=headers).content
# 解析数据保存到本地
with open(f'{download_title}-{download_id}.mp3', mode='wb') as f:
    f.write(music_content)
print(f"歌曲下载完成---，{download_title}--")